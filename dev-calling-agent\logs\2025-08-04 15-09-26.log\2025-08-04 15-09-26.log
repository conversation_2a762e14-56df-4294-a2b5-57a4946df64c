[ 2025-08-04 15:09:29,782 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 15:09:29,782 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 15:09:30,195 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4123 seconds.
[ 2025-08-04 15:09:34,053 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-base-en-v1.5
[ 2025-08-04 15:15:56,073 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 15:15:56,073 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 15:15:56,074 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 15:17:07,504 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 15:17:07,504 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 15:17:07,504 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 15:17:07,505 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 15:17:07,511 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 15:17:07,513 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 15:17:07,527 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 15:17:07,529 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 15:17:07,531 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 15:17:08,772 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 15:17:08,772 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 15:17:11,234 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 15:17:11,235 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 15:17:13,540 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 15:17:14,403 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-08-04 15:17:27,074 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 15:17:27,441 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:27,693 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-04 15:17:27,693 ] 207 root - INFO - \u26a1 Language detection: 0.251s
[ 2025-08-04 15:17:27,693 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 15:17:27,693 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-04 15:17:27,693 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 15:17:27,693 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-04 15:17:30,290 ] 59 root - INFO - \u26a1 Web search: 2.60s, 2 results
[ 2025-08-04 15:17:30,290 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5971 seconds.
[ 2025-08-04 15:17:30,290 ] 525 root - INFO - \U0001f310 Web search: 2.85s, 2 results
[ 2025-08-04 15:17:30,297 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:17:30,632 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:30,633 ] 513 root - INFO - Enhanced web search query: Narendra Modi biography as of 2025 as of 2025
[ 2025-08-04 15:17:30,635 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 15:17:30,636 ] 45 root - INFO - \U0001f310 Fast web search: 'Narendra Modi biography as of ...'
[ 2025-08-04 15:17:33,471 ] 59 root - INFO - \u26a1 Web search: 2.83s, 2 results
[ 2025-08-04 15:17:33,472 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8368 seconds.
[ 2025-08-04 15:17:33,472 ] 525 root - INFO - \U0001f310 Web search: 2.84s, 2 results
[ 2025-08-04 15:17:33,474 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:17:33,933 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:33,935 ] 513 root - INFO - Enhanced web search query: Narendra Modi current position as of 2025 as of 2025
[ 2025-08-04 15:17:33,937 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 15:17:33,939 ] 45 root - INFO - \U0001f310 Fast web search: 'Narendra Modi current position...'
[ 2025-08-04 15:17:36,220 ] 59 root - INFO - \u26a1 Web search: 2.28s, 2 results
[ 2025-08-04 15:17:36,221 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2836 seconds.
[ 2025-08-04 15:17:36,222 ] 525 root - INFO - \U0001f310 Web search: 2.29s, 2 results
[ 2025-08-04 15:17:36,224 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:17:36,987 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:36,989 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-04 15:17:36,990 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 15:17:36,990 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-04 15:17:39,810 ] 59 root - INFO - \u26a1 Web search: 2.82s, 2 results
[ 2025-08-04 15:17:39,812 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8220 seconds.
[ 2025-08-04 15:17:39,813 ] 525 root - INFO - \U0001f310 Web search: 2.82s, 2 results
[ 2025-08-04 15:17:39,816 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:17:43,669 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-04 15:17:55,518 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 15:17:55,990 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:55,991 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 15:17:55,991 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switch operating voltage...'
[ 2025-08-04 15:17:58,197 ] 129 root - ERROR - RAG invoke error: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:04 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '375', 'x-pinecone-request-id': '4351218740064054268', 'x-envoy-upstream-service-time': '73', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}

[ 2025-08-04 15:17:58,198 ] 65 root - ERROR - Vector database search error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:04 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '375', 'x-pinecone-request-id': '4351218740064054268', 'x-envoy-upstream-service-time': '73', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]
[ 2025-08-04 15:17:58,199 ] 478 root - ERROR - Vector tool error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\tools\vector_database_tool.py] line number [45] error message [Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:04 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '375', 'x-pinecone-request-id': '4351218740064054268', 'x-envoy-upstream-service-time': '73', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]]
[ 2025-08-04 15:17:58,201 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:17:58,554 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:17:58,555 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 15:17:58,555 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switch operating voltage...'
[ 2025-08-04 15:17:59,070 ] 129 root - ERROR - RAG invoke error: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '119', 'x-pinecone-request-id': '7850976403652783728', 'x-envoy-upstream-service-time': '32', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}

[ 2025-08-04 15:17:59,071 ] 65 root - ERROR - Vector database search error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '119', 'x-pinecone-request-id': '7850976403652783728', 'x-envoy-upstream-service-time': '32', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]
[ 2025-08-04 15:17:59,072 ] 478 root - ERROR - Vector tool error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\tools\vector_database_tool.py] line number [45] error message [Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:05 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '119', 'x-pinecone-request-id': '7850976403652783728', 'x-envoy-upstream-service-time': '32', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]]
[ 2025-08-04 15:17:59,073 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:18:06,726 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:18:06,732 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 15:18:06,738 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear operating voltage...'
[ 2025-08-04 15:18:07,267 ] 129 root - ERROR - RAG invoke error: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '145', 'x-pinecone-request-id': '5544523760893247447', 'x-envoy-upstream-service-time': '0', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}

[ 2025-08-04 15:18:07,269 ] 65 root - ERROR - Vector database search error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '145', 'x-pinecone-request-id': '5544523760893247447', 'x-envoy-upstream-service-time': '0', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]
[ 2025-08-04 15:18:07,271 ] 478 root - ERROR - Vector tool error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\tools\vector_database_tool.py] line number [45] error message [Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:13 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '145', 'x-pinecone-request-id': '5544523760893247447', 'x-envoy-upstream-service-time': '0', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]]
[ 2025-08-04 15:18:07,272 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:18:07,820 ] 496 livekit.agents - DEBUG - executing tool
[ 2025-08-04 15:18:07,821 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 15:18:07,822 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switch operating voltage...'
[ 2025-08-04 15:18:08,344 ] 129 root - ERROR - RAG invoke error: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:14 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '113', 'x-pinecone-request-id': '1368105326173173022', 'x-envoy-upstream-service-time': '1', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}

[ 2025-08-04 15:18:08,345 ] 65 root - ERROR - Vector database search error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:14 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '113', 'x-pinecone-request-id': '1368105326173173022', 'x-envoy-upstream-service-time': '1', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]
[ 2025-08-04 15:18:08,346 ] 478 root - ERROR - Vector tool error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\tools\vector_database_tool.py] line number [45] error message [Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Mon, 04 Aug 2025 09:48:14 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '113', 'x-pinecone-request-id': '1368105326173173022', 'x-envoy-upstream-service-time': '1', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]]
[ 2025-08-04 15:18:08,346 ] 575 livekit.agents - DEBUG - tools execution completed
[ 2025-08-04 15:18:08,347 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-04 15:18:28,337 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-08-04 15:18:28,338 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-08-04 15:18:28,347 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-08-04 15:18:28,347 ] 650 livekit.agents - DEBUG - session closed
[ 2025-08-04 15:18:28,347 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
