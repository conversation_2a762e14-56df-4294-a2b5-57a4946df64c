
import { Integration } from '@/types/integrations';

export const initialIntegrations: { [key: string]: Integration } = {
  // CRM Integrations - Enhanced
  salesforce: {
    id: 'salesforce',
    name: 'Salesforce CRM',
    category: 'crm',
    status: 'disconnected',
    config: {
      instanceUrl: '',
      clientId: '',
      clientSecret: '',
      username: '',
      password: '',
      securityToken: '',
      apiVersion: '58.0'
    },
    apiEndpoint: 'https://your-instance.salesforce.com',
    requiredFields: ['instanceUrl', 'clientId', 'clientSecret', 'username', 'password', 'securityToken'],
    webhookSupport: true,
    rateLimits: { requests: 15000, period: '24h' },
    regions: ['US', 'EU', 'APAC']
  },
  hubspot: {
    id: 'hubspot',
    name: 'HubSpot CRM',
    category: 'crm',
    status: 'disconnected',
    config: {
      apiKey: '',
      portalId: '',
      accessToken: ''
    },
    apiEndpoint: 'https://api.hubapi.com',
    requiredFields: ['apiKey', 'portalId'],
    webhookSupport: true,
    rateLimits: { requests: 40000, period: '24h' }
  },
  pipedrive: {
    id: 'pipedrive',
    name: 'Pipedrive CRM',
    category: 'crm',
    status: 'disconnected',
    config: {
      apiToken: '',
      companyDomain: ''
    },
    apiEndpoint: 'https://api.pipedrive.com/v1',
    requiredFields: ['apiToken', 'companyDomain'],
    webhookSupport: true
  },
  zoho: {
    id: 'zoho',
    name: 'Zoho CRM',
    category: 'crm',
    status: 'disconnected',
    config: {
      clientId: '',
      clientSecret: '',
      refreshToken: '',
      domain: 'com'
    },
    apiEndpoint: 'https://www.zohoapis.com/crm/v3',
    requiredFields: ['clientId', 'clientSecret', 'refreshToken'],
    webhookSupport: true
  },
  microsoft_dynamics: {
    id: 'microsoft_dynamics',
    name: 'Microsoft Dynamics 365',
    category: 'crm',
    status: 'disconnected',
    config: {
      tenantId: '',
      clientId: '',
      clientSecret: '',
      resource: '',
      environment: ''
    },
    apiEndpoint: 'https://your-org.crm.dynamics.com',
    requiredFields: ['tenantId', 'clientId', 'clientSecret', 'resource'],
    webhookSupport: true
  },
  freshsales: {
    id: 'freshsales',
    name: 'Freshsales CRM',
    category: 'crm',
    status: 'disconnected',
    config: {
      apiKey: '',
      domain: ''
    },
    apiEndpoint: 'https://domain.freshsales.io/api',
    requiredFields: ['apiKey', 'domain'],
    webhookSupport: true
  },

  // Telephony Integrations - Enhanced
  twilio: {
    id: 'twilio',
    name: 'Twilio Voice & SMS',
    category: 'telephony',
    status: 'disconnected',
    config: {
      accountSid: '',
      authToken: '',
      phoneNumber: '',
      applicationSid: '',
      webhookUrl: ''
    },
    apiEndpoint: 'https://api.twilio.com',
    requiredFields: ['accountSid', 'authToken', 'phoneNumber'],
    webhookSupport: true,
    rateLimits: { requests: 3600, period: '1h' }
  },
  vonage: {
    id: 'vonage',
    name: 'Vonage (Nexmo)',
    category: 'telephony',
    status: 'disconnected',
    config: {
      apiKey: '',
      apiSecret: '',
      applicationId: '',
      privateKey: ''
    },
    apiEndpoint: 'https://api.nexmo.com',
    requiredFields: ['apiKey', 'apiSecret'],
    webhookSupport: true
  },
  asterisk: {
    id: 'asterisk',
    name: 'Asterisk PBX',
    category: 'telephony',
    status: 'disconnected',
    config: {
      host: '',
      username: '',
      password: '',
      port: '8088',
      protocol: 'http'
    },
    apiEndpoint: 'http://your-asterisk:8088/ari',
    requiredFields: ['host', 'username', 'password'],
    webhookSupport: true
  },
  ringcentral: {
    id: 'ringcentral',
    name: 'RingCentral',
    category: 'telephony',
    status: 'disconnected',
    config: {
      clientId: '',
      clientSecret: '',
      serverUrl: 'https://platform.ringcentral.com',
      username: '',
      password: ''
    },
    apiEndpoint: 'https://platform.ringcentral.com',
    requiredFields: ['clientId', 'clientSecret', 'username', 'password'],
    webhookSupport: true
  },

  // Support Integrations - Enhanced
  zendesk: {
    id: 'zendesk',
    name: 'Zendesk Support',
    category: 'support',
    status: 'disconnected',
    config: {
      subdomain: '',
      email: '',
      apiToken: ''
    },
    apiEndpoint: 'https://your-subdomain.zendesk.com',
    requiredFields: ['subdomain', 'email', 'apiToken'],
    webhookSupport: true
  },
  servicenow: {
    id: 'servicenow',
    name: 'ServiceNow',
    category: 'support',
    status: 'disconnected',
    config: {
      instance: '',
      username: '',
      password: '',
      clientId: '',
      clientSecret: ''
    },
    apiEndpoint: 'https://your-instance.service-now.com',
    requiredFields: ['instance', 'username', 'password'],
    webhookSupport: true
  },
  intercom: {
    id: 'intercom',
    name: 'Intercom',
    category: 'support',
    status: 'disconnected',
    config: {
      accessToken: '',
      appId: ''
    },
    apiEndpoint: 'https://api.intercom.io',
    requiredFields: ['accessToken'],
    webhookSupport: true
  },
  freshdesk: {
    id: 'freshdesk',
    name: 'Freshdesk',
    category: 'support',
    status: 'disconnected',
    config: {
      domain: '',
      apiKey: ''
    },
    apiEndpoint: 'https://domain.freshdesk.com/api/v2',
    requiredFields: ['domain', 'apiKey'],
    webhookSupport: true
  },

  // Analytics Integrations - Enhanced
  tableau: {
    id: 'tableau',
    name: 'Tableau Analytics',
    category: 'analytics',
    status: 'disconnected',
    config: {
      serverUrl: '',
      username: '',
      password: '',
      siteName: '',
      personalAccessToken: ''
    },
    apiEndpoint: 'https://your-server.tableauonline.com',
    requiredFields: ['serverUrl', 'username', 'password'],
    webhookSupport: false
  },
  powerbi: {
    id: 'powerbi',
    name: 'Microsoft Power BI',
    category: 'analytics',
    status: 'disconnected',
    config: {
      tenantId: '',
      clientId: '',
      clientSecret: '',
      workspaceId: ''
    },
    apiEndpoint: 'https://api.powerbi.com',
    requiredFields: ['tenantId', 'clientId', 'clientSecret'],
    webhookSupport: true
  },
  grafana: {
    id: 'grafana',
    name: 'Grafana',
    category: 'analytics',
    status: 'disconnected',
    config: {
      url: '',
      apiKey: '',
      orgId: '1'
    },
    apiEndpoint: 'https://your-grafana.com/api',
    requiredFields: ['url', 'apiKey'],
    webhookSupport: true
  },
  mixpanel: {
    id: 'mixpanel',
    name: 'Mixpanel',
    category: 'analytics',
    status: 'disconnected',
    config: {
      projectToken: '',
      apiSecret: '',
      serviceAccountUsername: '',
      serviceAccountPassword: ''
    },
    apiEndpoint: 'https://mixpanel.com/api',
    requiredFields: ['projectToken', 'apiSecret'],
    webhookSupport: true
  },

  // Knowledge Base Integrations - Enhanced
  confluence: {
    id: 'confluence',
    name: 'Atlassian Confluence',
    category: 'knowledge',
    status: 'disconnected',
    config: {
      baseUrl: '',
      email: '',
      apiToken: '',
      spaceKey: ''
    },
    apiEndpoint: 'https://your-domain.atlassian.net',
    requiredFields: ['baseUrl', 'email', 'apiToken'],
    webhookSupport: true
  },
  notion: {
    id: 'notion',
    name: 'Notion',
    category: 'knowledge',
    status: 'disconnected',
    config: {
      integrationToken: '',
      databaseId: ''
    },
    apiEndpoint: 'https://api.notion.com/v1',
    requiredFields: ['integrationToken'],
    webhookSupport: false
  },
  sharepoint: {
    id: 'sharepoint',
    name: 'Microsoft SharePoint',
    category: 'knowledge',
    status: 'disconnected',
    config: {
      tenantId: '',
      clientId: '',
      clientSecret: '',
      siteUrl: ''
    },
    apiEndpoint: 'https://graph.microsoft.com',
    requiredFields: ['tenantId', 'clientId', 'clientSecret', 'siteUrl'],
    webhookSupport: true
  },
  elasticsearch: {
    id: 'elasticsearch',
    name: 'Elasticsearch',
    category: 'knowledge',
    status: 'disconnected',
    config: {
      host: '',
      port: '9200',
      username: '',
      password: '',
      index: '',
      ssl: true
    },
    apiEndpoint: 'https://your-elasticsearch:9200',
    requiredFields: ['host', 'index'],
    webhookSupport: false
  },

  // AI Enhancement Integrations - New Category
  openai: {
    id: 'openai',
    name: 'OpenAI GPT',
    category: 'ai_enhancement',
    status: 'disconnected',
    config: {
      apiKey: '',
      organization: '',
      model: 'gpt-4',
      temperature: '0.7',
      maxTokens: '2048'
    },
    apiEndpoint: 'https://api.openai.com/v1',
    requiredFields: ['apiKey'],
    webhookSupport: false,
    rateLimits: { requests: 3500, period: '1m' }
  },
  anthropic: {
    id: 'anthropic',
    name: 'Anthropic Claude',
    category: 'ai_enhancement',
    status: 'disconnected',
    config: {
      apiKey: '',
      model: 'claude-3-opus-20240229',
      maxTokens: '4096'
    },
    apiEndpoint: 'https://api.anthropic.com',
    requiredFields: ['apiKey'],
    webhookSupport: false
  },
  huggingface: {
    id: 'huggingface',
    name: 'Hugging Face',
    category: 'ai_enhancement',
    status: 'disconnected',
    config: {
      apiKey: '',
      modelEndpoint: '',
      customModel: ''
    },
    apiEndpoint: 'https://api-inference.huggingface.co',
    requiredFields: ['apiKey'],
    webhookSupport: false
  },

  // Security & Compliance - New Category
  vault: {
    id: 'vault',
    name: 'HashiCorp Vault',
    category: 'security',
    status: 'disconnected',
    config: {
      address: '',
      token: '',
      namespace: '',
      mount: 'secret'
    },
    apiEndpoint: 'https://your-vault:8200',
    requiredFields: ['address', 'token'],
    webhookSupport: false
  },
  okta: {
    id: 'okta',
    name: 'Okta Identity',
    category: 'security',
    status: 'disconnected',
    config: {
      domain: '',
      clientId: '',
      clientSecret: '',
      apiToken: ''
    },
    apiEndpoint: 'https://your-domain.okta.com',
    requiredFields: ['domain', 'apiToken'],
    webhookSupport: true
  }
};
