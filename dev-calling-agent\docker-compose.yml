version: '3.8'

services:
  # LiveKit Server
  livekit-server:
    image: livekit/livekit-dev-:latest
    ports:
      - "7880:7880"  # HTTP API
      - "7881:7881"  # WebRTC
    volumes:
      - ./livekit.yaml:/livekit.yaml
    command: --config /livekit.yaml
    # environment:
    #   - LIVEKIT_KEYS=devkey:secret
    networks:
      - livekit-network

  # Redis (for LiveKit)
  redis:
    image: redis:redis-7.0-alpine
    ports:
      - "6379:6379"
    networks:
      - livekit-network

  # FastAPI Application
  fastapi-app:
    build:
      context: ./fastapi-app
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./fastapi-app:/app
    environment:
      - LIVEKIT_URL=wss://test-wazew3xi.livekit.cloud
      - LIVEKIT_API_KEY=APIQH2uLeCEwF7g
      - LIVEKIT_API_SECRET=7iyVxaprihi2uqNf4NG5kGLZJHYIE5B058SOAI2jBei
      - REDIS_URL=redis://redis:6379
    depends_on:
      - livekit-server
      - redis
    networks:
      - livekit-network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

networks:
  livekit-network:
    driver: bridge