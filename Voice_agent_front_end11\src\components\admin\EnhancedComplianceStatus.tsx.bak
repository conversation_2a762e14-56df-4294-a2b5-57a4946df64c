
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  Download, 
  Eye, 
  Calendar,
  FileText,
  Lock,
  Globe,
  Users,
  Database
} from 'lucide-react';

export const EnhancedComplianceStatus = () => {
  const [selectedCompliance, setSelectedCompliance] = useState<string | null>(null);

  const complianceItems = [
    {
      name: 'GDPR',
      status: 'Compliant',
      lastAudit: '2024-01-10',
      nextAudit: '2024-04-10',
      isCompliant: true,
      score: 98,
      category: 'Data Protection',
      details: {
        description: 'General Data Protection Regulation compliance for EU data handling',
        requirements: ['Data encryption', 'Right to be forgotten', 'Consent management'],
        evidence: ['Data processing agreement', 'Privacy impact assessment', 'Consent records']
      }
    },
    {
      name: 'SOC 2 Type II',
      status: 'Compliant',
      lastAudit: '2024-01-15',
      nextAudit: '2024-07-15',
      isCompliant: true,
      score: 95,
      category: 'Security',
      details: {
        description: 'Service Organization Control 2 certification for security controls',
        requirements: ['Security monitoring', 'Access controls', 'Change management'],
        evidence: ['Security policies', 'Access logs', 'Incident reports']
      }
    },
    {
      name: 'ISO/IEC 27001',
      status: 'Compliant',
      lastAudit: '2023-12-20',
      nextAudit: '2024-12-20',
      isCompliant: true,
      score: 92,
      category: 'Information Security',
      details: {
        description: 'International standard for information security management systems',
        requirements: ['Risk assessment', 'Security controls', 'Continuous monitoring'],
        evidence: ['ISMS documentation', 'Risk register', 'Control assessments']
      }
    },
    {
      name: 'HIPAA',
      status: 'Under Review',
      lastAudit: '2024-01-05',
      nextAudit: '2024-03-05',
      isCompliant: false,
      score: 78,
      category: 'Healthcare',
      details: {
        description: 'Health Insurance Portability and Accountability Act compliance',
        requirements: ['PHI encryption', 'Access controls', 'Audit logging'],
        evidence: ['BAA agreements', 'Encryption certificates', 'Access logs']
      }
    },
    {
      name: 'DORA',
      status: 'Compliant',
      lastAudit: '2024-01-12',
      nextAudit: '2024-06-12',
      isCompliant: true,
      score: 89,
      category: 'Financial Services',
      details: {
        description: 'Digital Operational Resilience Act for financial services',
        requirements: ['Incident reporting', 'Third-party risk', 'Testing'],
        evidence: ['Incident procedures', 'Risk assessments', 'Test reports']
      }
    },
    {
      name: 'EU AI Act',
      status: 'Compliant',
      lastAudit: '2024-01-08',
      nextAudit: '2024-04-08',
      isCompliant: true,
      score: 85,
      category: 'AI Governance',
      details: {
        description: 'European Union Artificial Intelligence Act compliance',
        requirements: ['AI risk assessment', 'Transparency', 'Human oversight'],
        evidence: ['AI impact assessment', 'Model documentation', 'Oversight procedures']
      }
    }
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Data Protection': return <Lock className="w-4 h-4" />;
      case 'Security': return <Shield className="w-4 h-4" />;
      case 'Information Security': return <Database className="w-4 h-4" />;
      case 'Healthcare': return <Users className="w-4 h-4" />;
      case 'Financial Services': return <Globe className="w-4 h-4" />;
      case 'AI Governance': return <FileText className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Shield className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold text-white">Compliance Dashboard</h3>
            <Badge variant="outline" className="text-green-400 border-green-400">
              5/6 Compliant
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="bg-gradient-to-r from-pink-500 to-purple-600 text-white">
              <Calendar className="w-4 h-4 mr-2" />
              Schedule Audit
            </Button>
            <Button variant="outline" size="sm" className="bg-gradient-to-r from-pink-500 to-purple-600 text-white">
              <Download className="w-4 h-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {complianceItems.map((item, index) => (
            <div 
              key={index} 
              className="p-4 bg-gray-900/50 rounded-lg border border-gray-700 hover:border-gray-600 transition-colors cursor-pointer"
              onClick={() => setSelectedCompliance(selectedCompliance === item.name ? null : item.name)}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  {getCategoryIcon(item.category)}
                  <h4 className="font-medium text-white">{item.name}</h4>
                </div>
                <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                  item.isCompliant 
                    ? 'bg-green-500/20 text-green-400' 
                    : 'bg-yellow-500/20 text-yellow-400'
                }`}>
                  {item.isCompliant ? (
                    <CheckCircle className="w-3 h-3" />
                  ) : (
                    <AlertTriangle className="w-3 h-3" />
                  )}
                  <span>{item.status}</span>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Compliance Score</span>
                    <span className="text-white">{item.score}%</span>
                  </div>
                  <Progress value={item.score} className="h-2" />
                </div>
                
                <div className="space-y-1 text-sm text-gray-400">
                  <div className="flex justify-between">
                    <span>Category:</span>
                    <span className="text-white">{item.category}</span>
                  </div>
                  <div>Last Audit: {item.lastAudit}</div>
                  <div>Next Audit: {item.nextAudit}</div>
                </div>
                
                {selectedCompliance === item.name && (
                  <div className="mt-4 p-3 bg-gray-800/50 rounded border border-gray-600">
                    <p className="text-sm text-gray-300 mb-2">{item.details.description}</p>
                    <div className="space-y-2">
                      <div>
                        <span className="text-xs text-gray-400">Requirements:</span>
                        <ul className="text-xs text-gray-300 ml-2">
                          {item.details.requirements.map((req, idx) => (
                            <li key={idx}>� {req}</li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <span className="text-xs text-gray-400">Evidence:</span>
                        <ul className="text-xs text-gray-300 ml-2">
                          {item.details.evidence.map((ev, idx) => (
                            <li key={idx}>� {ev}</li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="mt-2 text-blue-400 hover:text-blue-300">
                      <Eye className="w-3 h-3 mr-1" />
                      View Details
                    </Button>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
