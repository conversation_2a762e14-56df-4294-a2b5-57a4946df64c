
import React, { useState } from 'react';
import { AdminAuthentication } from '@/components/admin/AdminAuthentication';
import { AdminHeader } from '@/components/admin/AdminHeader';
import { RealTimeStatsCards } from '@/components/admin/RealTimeStatsCards';
import { EnhancedComplianceStatus } from '@/components/admin/EnhancedComplianceStatus';
import { DataHandlingOverview } from '@/components/admin/DataHandlingOverview';
import { NavigationTabs } from '@/components/admin/NavigationTabs';
import { CallLogsTable } from '@/components/admin/CallLogsTable';
import { AdvancedAnalytics } from '@/components/admin/AdvancedAnalytics';
import { AuditTrail } from '@/components/admin/AuditTrail';
import { DashboardWidgets } from '@/components/admin/DashboardWidgets';
import { EnhancedAPIConfiguration } from '@/components/admin/EnhancedAPIConfiguration';
import { DataProcessingDetails } from '@/components/admin/DataProcessingDetails';
import { ComplianceConfiguration } from '@/components/admin/ComplianceConfiguration';
import { EnterpriseIntegrations } from '@/components/admin/EnterpriseIntegrations';
import { EnhancedRealTimeProcessor } from '@/components/admin/EnhancedRealTimeProcessor';
import { UserManagement } from '@/components/admin/UserManagement';

type UserRole = 'super_admin' | 'admin' | 'viewer';

const Admin = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userRole, setUserRole] = useState<UserRole>('viewer');
  const [activeTab, setActiveTab] = useState('overview');

  const handleLogin = (role: UserRole) => {
    setUserRole(role);
    setIsLoggedIn(true);
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUserRole('viewer');
    setActiveTab('overview');
  };

  if (!isLoggedIn) {
    return <AdminAuthentication onLogin={handleLogin} />;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-8">
            <RealTimeStatsCards />
            <DashboardWidgets />
            <EnhancedRealTimeProcessor />
            <div className="grid lg:grid-cols-1 gap-8">
              <CallLogsTable />
            </div>
          </div>
        );
      case 'logs':
        return (
          <div className="space-y-8">
            <CallLogsTable />
            <AuditTrail />
          </div>
        );
      case 'metrics':
        return <AdvancedAnalytics />;
      case 'compliance':
        return (
          <div className="space-y-8">
            <EnhancedComplianceStatus />
            <DataProcessingDetails />
            <DataHandlingOverview />
            <AuditTrail />
          </div>
        );
      case 'settings':
        return (
          <div className="space-y-8">
            {userRole === 'super_admin' && <UserManagement />}
            <EnhancedAPIConfiguration />
            <ComplianceConfiguration />
            <EnterpriseIntegrations />
            <AuditTrail />
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      <AdminHeader onLogout={handleLogout} userRole={userRole} />

      <main className="container mx-auto px-4 py-8">
        <NavigationTabs activeTab={activeTab} onTabChange={setActiveTab} userRole={userRole} />
        {renderTabContent()}
      </main>
    </div>
  );
};

export default Admin;
