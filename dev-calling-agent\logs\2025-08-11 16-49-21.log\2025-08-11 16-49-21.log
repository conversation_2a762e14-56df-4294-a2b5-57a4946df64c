[ 2025-08-11 16:49:25,797 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 16:49:25,809 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,811 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,811 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,812 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,812 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,812 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,813 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,815 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,818 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,818 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,819 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,819 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 16:49:25,833 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,833 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,833 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,834 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,834 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,835 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,835 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,836 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,836 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,836 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,837 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:25,837 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 16:49:26,440 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 16:49:33,783 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 16:49:33,903 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 16:49:34,443 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5401 seconds.
[ 2025-08-11 16:49:39,952 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 16:49:49,170 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 16:49:49,171 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 16:49:49,172 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 16:49:50,105 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 16.20 seconds
[ 2025-08-11 16:50:14,183 ] 218 root - INFO - \U0001f4f1 DTMF received: 1
[ 2025-08-11 16:50:14,184 ] 62 livekit - ERROR - failed to emit event sip_dtmf_received
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\rtc\event_emitter.py", line 58, in emit
    callback(*callback_args)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 226, in handle_dtmf
    agent.update_language_instructions(digit)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 156, in update_language_instructions
    self.instructions = config['instructions']
    ^^^^^^^^^^^^^^^^^
AttributeError: property 'instructions' of 'UnifiedLanguageAgent' object has no setter
[ 2025-08-11 16:50:17,950 ] 302 root - ERROR - Unified agent error: 'EnhancedCallSession' object has no attribute 'wait_for_completion'
[ 2025-08-11 16:50:17,952 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 299, in entrypoint
    await session.wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedCallSession' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 303, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\unified_agent.py] line number [299] error message ['EnhancedCallSession' object has no attribute 'wait_for_completion']
[ 2025-08-11 16:50:39,130 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 16:50:47,585 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 16:50:48,490 ] 107 livekit.agents - WARNING - exiting forcefully
