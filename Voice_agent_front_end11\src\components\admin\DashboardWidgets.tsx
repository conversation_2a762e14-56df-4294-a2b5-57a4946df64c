
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  <PERSON>hart,
  Pie,
  Cell
} from 'recharts';
import { 
  Settings, 
  Maximize2, 
  Minimize2, 
  RotateCcw,
  Globe,
  MessageSquare,
  Users,
  Clock
} from 'lucide-react';

export const DashboardWidgets = () => {
  const [expandedWidget, setExpandedWidget] = useState<string | null>(null);

  const languageData = [
    { language: 'English', calls: 567, percentage: 45.5 },
    { language: 'German', calls: 234, percentage: 18.8 },
    { language: 'Turkish', calls: 189, percentage: 15.2 },
    { language: 'French', calls: 156, percentage: 12.5 },
    { language: 'Spanish', calls: 101, percentage: 8.1 }
  ];

  const genderUsageData = [
    { name: 'Female Voice', value: 678, color: '#8B5CF6' },
    { name: 'Male Voice', value: 569, color: '#06B6D4' }
  ];

  const intentHeatmapData = [
    { intent: 'Account Help', count: 234, confidence: 94 },
    { intent: 'Billing Support', count: 189, confidence: 87 },
    { intent: 'Technical Issues', count: 156, confidence: 91 },
    { intent: 'General Inquiry', count: 145, confidence: 82 },
    { intent: 'Cancellation', count: 89, confidence: 95 }
  ];

  const responseTimeData = [
    { time: '00:00', avgTime: 2.1 },
    { time: '04:00', avgTime: 1.8 },
    { time: '08:00', avgTime: 2.5 },
    { time: '12:00', avgTime: 3.2 },
    { time: '16:00', avgTime: 2.8 },
    { time: '20:00', avgTime: 2.3 }
  ];

  const Widget = ({ 
    id, 
    title, 
    icon, 
    children, 
    className = "" 
  }: { 
    id: string;
    title: string;
    icon: React.ReactNode;
    children: React.ReactNode;
    className?: string;
  }) => {
    const isExpanded = expandedWidget === id;
    
    return (
      <Card className={`p-4 bg-gray-800/50 border-gray-700 ${className} ${isExpanded ? 'col-span-2 row-span-2' : ''}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {icon}
            <h3 className="text-lg font-semibold text-white">{title}</h3>
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setExpandedWidget(isExpanded ? null : id)}
              className="text-gray-400 hover:text-white"
            >
              {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>
            <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
        {children}
      </Card>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 auto-rows-fr">
      <Widget
        id="languages"
        title="Top Languages"
        icon={<Globe className="w-5 h-5 text-blue-400" />}
        className="lg:col-span-2"
      >
        <div className="space-y-3">
          {languageData.map((lang, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <span className="text-blue-400 text-xs font-medium">
                    {lang.language.slice(0, 2).toUpperCase()}
                  </span>
                </div>
                <span className="text-white">{lang.language}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-300">{lang.calls}</span>
                <Badge variant="outline" className="text-blue-400 border-blue-400">
                  {lang.percentage}%
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </Widget>

      <Widget
        id="voice-gender"
        title="Voice Gender Usage"
        icon={<Users className="w-5 h-5 text-purple-400" />}
      >
        <ResponsiveContainer width="100%" height={200}>
          <PieChart>
            <Pie
              data={genderUsageData}
              cx="50%"
              cy="50%"
              innerRadius={40}
              outerRadius={80}
              dataKey="value"
            >
              {genderUsageData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F3F4F6'
              }} 
            />
          </PieChart>
        </ResponsiveContainer>
        <div className="flex justify-center space-x-4 mt-2">
          {genderUsageData.map((entry, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div 
                className="w-3 h-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span className="text-gray-300 text-sm">{entry.name}</span>
            </div>
          ))}
        </div>
      </Widget>

      <Widget
        id="intent-heatmap"
        title="Intent Heatmap"
        icon={<MessageSquare className="w-5 h-5 text-green-400" />}
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height={200}>
          <BarChart data={intentHeatmapData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="intent" 
              stroke="#9CA3AF" 
              fontSize={12}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis stroke="#9CA3AF" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F3F4F6'
              }} 
            />
            <Bar dataKey="count" fill="#10B981" />
          </BarChart>
        </ResponsiveContainer>
      </Widget>

      <Widget
        id="response-time"
        title="Response Time Trend"
        icon={<Clock className="w-5 h-5 text-orange-400" />}
        className="lg:col-span-2"
      >
        <ResponsiveContainer width="100%" height={200}>
          <LineChart data={responseTimeData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="time" stroke="#9CA3AF" />
            <YAxis stroke="#9CA3AF" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937', 
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F3F4F6'
              }} 
            />
            <Line 
              type="monotone" 
              dataKey="avgTime" 
              stroke="#F59E0B" 
              strokeWidth={2}
              dot={{ fill: '#F59E0B', strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </Widget>
    </div>
  );
};
