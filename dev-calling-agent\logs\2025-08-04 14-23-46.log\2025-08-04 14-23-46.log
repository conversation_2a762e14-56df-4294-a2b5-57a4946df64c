[ 2025-08-04 14:24:03,200 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 14:24:03,201 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 14:24:03,832 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.6307 seconds.
[ 2025-08-04 14:25:03,038 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-04 14:25:13,096 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 14:25:13,097 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 14:25:13,098 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 14:26:24,291 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 14:26:24,291 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 14:26:24,292 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 14:26:24,292 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 14:26:24,302 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 14:26:24,355 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,357 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,359 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,361 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,362 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,362 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,364 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,364 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,365 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,365 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,366 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,367 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:26:24,418 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,420 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,420 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,421 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,422 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,422 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,423 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,424 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,425 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,425 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,429 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,431 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:26:24,984 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-04 14:26:41,703 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-04 14:26:41,704 ] 560 livekit.agents - INFO - shutting down worker
