{"timestamp": "2025-08-07T12:08:13.249687", "call_id": "SCL_PQrpBFdz5nTi", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:08:17.925647", "call_id": "SCL_PQrpBFdz5nTi", "event_type": "CALL_ENDED", "data": {"duration": 4.674448, "end_time": "2025-08-07T12:08:17.924135"}}
{"timestamp": "2025-08-07T12:09:53.711834", "call_id": "SCL_yjhViwVdZZ7X", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:09:57.955514", "call_id": "SCL_yjhViwVdZZ7X", "event_type": "CALL_ENDED", "data": {"duration": 4.240639, "end_time": "2025-08-07T12:09:57.952473"}}
{"timestamp": "2025-08-07T12:13:05.571523", "call_id": "SCL_YEkGnCp47CYg", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:13:11.041641", "call_id": "SCL_YEkGnCp47CYg", "event_type": "CALL_ENDED", "data": {"duration": 5.468113, "end_time": "2025-08-07T12:13:11.039636"}}
{"timestamp": "2025-08-07T12:15:59.076210", "call_id": "SCL_he6k2i262n2H", "event_type": "CALL_STARTED", "data": {"caller": "+916290438432", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:16:02.842665", "call_id": "SCL_he6k2i262n2H", "event_type": "CALL_ENDED", "data": {"duration": 3.764943, "end_time": "2025-08-07T12:16:02.841153"}}
{"timestamp": "2025-08-07T12:16:13.272592", "call_id": "SCL_YMVxXWe5gHDY", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:16:18.947070", "call_id": "SCL_YMVxXWe5gHDY", "event_type": "CALL_ENDED", "data": {"duration": 5.669898, "end_time": "2025-08-07T12:16:18.942490"}}
{"timestamp": "2025-08-07T12:24:20.890902", "call_id": "SCL_u6MyYeK353ss", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:24:25.986595", "call_id": "SCL_u6MyYeK353ss", "event_type": "CALL_ENDED", "data": {"duration": 5.093615, "end_time": "2025-08-07T12:24:25.984517"}}
{"timestamp": "2025-08-07T12:29:37.495150", "call_id": "SCL_9v4gzUsh2WAA", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T12:29:42.790723", "call_id": "SCL_9v4gzUsh2WAA", "event_type": "CALL_ENDED", "data": {"duration": 5.291311, "end_time": "2025-08-07T12:29:42.786461"}}
{"timestamp": "2025-08-07T16:41:19.611047", "call_id": "SCL_Q22LHmAX5oud", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:41:30.206844", "call_id": "SCL_Q22LHmAX5oud", "event_type": "CALL_ENDED", "data": {"duration": 10.591746, "end_time": "2025-08-07T16:41:30.202793"}}
{"timestamp": "2025-08-07T16:42:56.692011", "call_id": "SCL_Rtr26K98p6rn", "event_type": "CALL_STARTED", "data": {"caller": "+918686860563", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:43:09.739306", "call_id": "SCL_Rtr26K98p6rn", "event_type": "CALL_ENDED", "data": {"duration": 13.040824, "end_time": "2025-08-07T16:43:09.732835"}}
{"timestamp": "2025-08-07T16:43:33.634995", "call_id": "SCL_iMP7sp9gKPGA", "event_type": "CALL_STARTED", "data": {"caller": "+918686860563", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:44:00.685671", "call_id": "SCL_aucYER2b3qo8", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:44:02.627470", "call_id": "SCL_aucYER2b3qo8", "event_type": "CALL_ENDED", "data": {"duration": 1.930299, "end_time": "2025-08-07T16:44:02.615454"}}
{"timestamp": "2025-08-07T16:47:30.277513", "call_id": "SCL_d6xq7KDhpJPR", "event_type": "CALL_STARTED", "data": {"caller": "+918686860563", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:47:37.893724", "call_id": "SCL_d6xq7KDhpJPR", "event_type": "CALL_ENDED", "data": {"duration": 7.612489, "end_time": "2025-08-07T16:47:37.889493"}}
{"timestamp": "2025-08-07T16:50:53.334737", "call_id": "SCL_k8c4yG5zhfKj", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:50:58.833452", "call_id": "SCL_k8c4yG5zhfKj", "event_type": "CALL_ENDED", "data": {"duration": 5.493726, "end_time": "2025-08-07T16:50:58.828463"}}
{"timestamp": "2025-08-07T16:55:18.578518", "call_id": "SCL_Akhqmz5uBkSd", "event_type": "CALL_STARTED", "data": {"caller": "+916295716352", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:55:24.982518", "call_id": "SCL_Akhqmz5uBkSd", "event_type": "CALL_ENDED", "data": {"duration": 6.400446, "end_time": "2025-08-07T16:55:24.978964"}}
{"timestamp": "2025-08-07T16:59:18.597609", "call_id": "SCL_LncBTWnB7bX5", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T16:59:26.039935", "call_id": "SCL_LncBTWnB7bX5", "event_type": "CALL_ENDED", "data": {"duration": 7.440631, "end_time": "2025-08-07T16:59:26.038240"}}
{"timestamp": "2025-08-07T17:07:36.160074", "call_id": "SCL_hX5E9whNkSfS", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T17:07:43.740852", "call_id": "SCL_hX5E9whNkSfS", "event_type": "CALL_ENDED", "data": {"duration": 7.579616, "end_time": "2025-08-07T17:07:43.739690"}}
{"timestamp": "2025-08-07T17:10:27.673180", "call_id": "SCL_GPSLrKEtLZaT", "event_type": "CALL_STARTED", "data": {"caller": "+918686860563", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T17:10:33.416260", "call_id": "SCL_GPSLrKEtLZaT", "event_type": "CALL_ENDED", "data": {"duration": 5.739599, "end_time": "2025-08-07T17:10:33.412779"}}
{"timestamp": "2025-08-07T17:12:00.809191", "call_id": "SCL_dQbMxmTaxdUS", "event_type": "CALL_STARTED", "data": {"caller": "+918686860563", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T17:12:06.479444", "call_id": "SCL_dQbMxmTaxdUS", "event_type": "CALL_ENDED", "data": {"duration": 5.667182, "end_time": "2025-08-07T17:12:06.476373"}}
{"timestamp": "2025-08-07T17:40:02.316030", "call_id": "SCL_YwWNPWLtSGwp", "event_type": "CALL_STARTED", "data": {"caller": "+918885161078", "trunk": "+17622426327", "status": "ringing"}}
{"timestamp": "2025-08-07T17:40:11.188038", "call_id": "SCL_YwWNPWLtSGwp", "event_type": "CALL_ENDED", "data": {"duration": 8.866435, "end_time": "2025-08-07T17:40:11.182465"}}
