[ 2025-08-11 17:40:27,322 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 17:40:27,331 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,331 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,332 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,333 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,333 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,335 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,336 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,336 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,341 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,343 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,343 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,343 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:40:27,356 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,356 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,357 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,357 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,358 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,360 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:27,360 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:40:28,291 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 17:40:35,859 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 17:40:35,943 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 17:40:36,400 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4565 seconds.
[ 2025-08-11 17:40:40,611 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 17:40:49,646 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 17:40:49,646 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 17:40:49,646 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 17:40:50,397 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.45 seconds
[ 2025-08-11 17:40:52,894 ] 385 root - ERROR - Unified agent error: AgentSession.say() got an unexpected keyword argument 'language'
[ 2025-08-11 17:40:52,894 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 368, in entrypoint
    await session.say(
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 228, in say
    return await super().say(text, **kwargs)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: AgentSession.say() got an unexpected keyword argument 'language'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 386, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\unified_agent.py] line number [368] error message [AgentSession.say() got an unexpected keyword argument 'language']
[ 2025-08-11 17:41:23,911 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 17:41:45,609 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-11 17:41:45,782 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:41:45,784 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:45:42,496 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 17:45:42,497 ] 560 livekit.agents - INFO - shutting down worker
