
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { X, Phone } from 'lucide-react';

interface CallTransferNotificationProps {
  onClose: () => void;
}

export const CallTransferNotification = ({ onClose }: CallTransferNotificationProps) => {
  return (
    <div className="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-sm animate-fade-in">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <Phone className="w-5 h-5 text-blue-600" />
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-medium text-gray-900">
            Transferring to Human Agent
          </h4>
          <p className="text-sm text-gray-500 mt-1">
            Please hold while we connect you to a human representative.
          </p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="flex-shrink-0 h-auto p-1"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
};
