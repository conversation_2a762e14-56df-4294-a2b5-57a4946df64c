
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  MessageSquare, 
  ArrowRight, 
  CheckCircle, 
  Clock,
  Target,
  TrendingUp
} from 'lucide-react';

interface ConversationFlowProps {
  conversationHistory: any[];
  currentState: string;
  onStateChange: (newState: string) => void;
}

export const ConversationFlow = ({ 
  conversationHistory, 
  currentState, 
  onStateChange 
}: ConversationFlowProps) => {
  const conversationStates = [
    { id: 'greeting', name: 'Greeting', color: 'bg-blue-500' },
    { id: 'problem_identification', name: 'Problem ID', color: 'bg-yellow-500' },
    { id: 'information_gathering', name: 'Info Gathering', color: 'bg-orange-500' },
    { id: 'solution_proposal', name: 'Solution', color: 'bg-purple-500' },
    { id: 'resolution', name: '<PERSON>', color: 'bg-green-500' },
    { id: 'follow_up', name: 'Follow-up', color: 'bg-indigo-500' }
  ];

  const getCurrentStateIndex = () => {
    return conversationStates.findIndex(state => state.id === currentState);
  };

  const getProgressPercentage = () => {
    const currentIndex = getCurrentStateIndex();
    return ((currentIndex + 1) / conversationStates.length) * 100;
  };

  const getNextRecommendedActions = () => {
    const actions = {
      greeting: [
        'Ask about the customer\'s specific issue',
        'Gather initial problem details',
        'Set expectations for the conversation'
      ],
      problem_identification: [
        'Ask clarifying questions',
        'Identify root cause',
        'Categorize the issue type'
      ],
      information_gathering: [
        'Collect relevant account details',
        'Understand customer preferences',
        'Review previous interactions'
      ],
      solution_proposal: [
        'Present tailored solutions',
        'Explain step-by-step process',
        'Offer alternative options'
      ],
      resolution: [
        'Confirm solution effectiveness',
        'Document resolution details',
        'Set up monitoring if needed'
      ],
      follow_up: [
        'Schedule follow-up if needed',
        'Provide additional resources',
        'Gather feedback on experience'
      ]
    };

    return actions[currentState as keyof typeof actions] || [];
  };

  const getConversationMetrics = () => {
    const totalExchanges = Math.floor(conversationHistory.length / 2);
    const avgResponseTime = 2.3; // Simulated
    const resolutionRate = 87; // Simulated
    
    return {
      exchanges: totalExchanges,
      avgResponseTime,
      resolutionRate
    };
  };

  const metrics = getConversationMetrics();

  return (
    <div className="space-y-4">
      {/* Conversation Progress */}
      <Card className="p-4 bg-gray-900/50 border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-semibold flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Conversation Flow
          </h3>
          <Badge variant="outline" className="text-blue-400 border-blue-400">
            {Math.round(getProgressPercentage())}% Complete
          </Badge>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>

        {/* State Flow */}
        <div className="flex items-center space-x-2 overflow-x-auto pb-2">
          {conversationStates.map((state, index) => {
            const isActive = state.id === currentState;
            const isCompleted = index < getCurrentStateIndex();
            
            return (
              <React.Fragment key={state.id}>
                <div
                  className={`flex flex-col items-center space-y-1 min-w-0 flex-shrink-0 ${
                    isActive ? 'opacity-100' : isCompleted ? 'opacity-80' : 'opacity-40'
                  }`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-medium ${
                      isCompleted ? 'bg-green-500' : isActive ? state.color : 'bg-gray-600'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <span className="text-xs text-gray-300 text-center px-1">
                    {state.name}
                  </span>
                </div>
                
                {index < conversationStates.length - 1 && (
                  <ArrowRight className="w-4 h-4 text-gray-500 flex-shrink-0" />
                )}
              </React.Fragment>
            );
          })}
        </div>
      </Card>

      {/* Metrics */}
      <div className="grid grid-cols-3 gap-4">
        <Card className="p-3 bg-gray-900/50 border-gray-700">
          <div className="flex items-center space-x-2">
            <MessageSquare className="w-4 h-4 text-blue-400" />
            <div>
              <p className="text-gray-400 text-xs">Exchanges</p>
              <p className="text-white font-semibold">{metrics.exchanges}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-3 bg-gray-900/50 border-gray-700">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-green-400" />
            <div>
              <p className="text-gray-400 text-xs">Avg Response</p>
              <p className="text-white font-semibold">{metrics.avgResponseTime}s</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-3 bg-gray-900/50 border-gray-700">
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4 text-purple-400" />
            <div>
              <p className="text-gray-400 text-xs">Success Rate</p>
              <p className="text-white font-semibold">{metrics.resolutionRate}%</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Next Actions */}
      <Card className="p-4 bg-gray-900/50 border-gray-700">
        <h4 className="text-white font-medium mb-3">Recommended Next Actions</h4>
        <div className="space-y-2">
          {getNextRecommendedActions().map((action, index) => (
            <div key={index} className="flex items-center space-x-2 text-sm">
              <div className="w-2 h-2 bg-blue-400 rounded-full" />
              <span className="text-gray-300">{action}</span>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};
