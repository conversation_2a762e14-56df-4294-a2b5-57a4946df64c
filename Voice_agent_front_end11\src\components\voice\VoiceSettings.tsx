
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { VoiceType, VoiceGender } from '@/pages/Index';
import { Mic } from 'lucide-react';

interface VoiceSettingsProps {
  voiceType: VoiceType;
  voiceGender: VoiceGender;
  onVoiceTypeChange: (type: VoiceType) => void;
  onVoiceGenderChange: (gender: VoiceGender) => void;
}

export const VoiceSettings = ({ 
  voiceType, 
  voiceGender, 
  onVoiceTypeChange, 
  onVoiceGenderChange 
}: VoiceSettingsProps) => {
  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30 shadow-xl">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Mic className="w-5 h-5 mr-2" />
        Voice Settings
      </h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-white text-sm font-medium mb-2">Voice Type</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={voiceType === 'formal' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceTypeChange('formal')}
              className={voiceType === 'formal' 
                ? 'bg-white text-purple-600' 
                : 'border-white/30 text-white hover:bg-white/20'
              }
            >
              Formal
            </Button>
            <Button
              variant={voiceType === 'casual' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceTypeChange('casual')}
              className={voiceType === 'casual' 
                ? 'bg-white text-purple-600' 
                : 'border-white/30 text-white hover:bg-white/20'
              }
            >
              Casual
            </Button>
          </div>
        </div>

        <div>
          <label className="block text-white text-sm font-medium mb-2">Voice Gender</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={voiceGender === 'male' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceGenderChange('male')}
              className={voiceGender === 'male' 
                ? 'bg-white text-purple-600' 
                : 'border-white/30 text-white hover:bg-white/20'
              }
            >
              Male
            </Button>
            <Button
              variant={voiceGender === 'female' ? 'default' : 'outline'}
              size="sm"
              onClick={() => onVoiceGenderChange('female')}
              className={voiceGender === 'female' 
                ? 'bg-white text-purple-600' 
                : 'border-white/30 text-white hover:bg-white/20'
              }
            >
              Female
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
