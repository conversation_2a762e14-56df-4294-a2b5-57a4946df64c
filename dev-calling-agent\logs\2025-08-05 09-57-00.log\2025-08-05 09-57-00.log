[ 2025-08-05 09:57:04,246 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-05 09:57:04,246 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-05 09:57:04,671 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4249 seconds.
[ 2025-08-05 09:57:08,552 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-base-en-v1.5
[ 2025-08-05 09:57:18,180 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 09:57:18,180 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 09:57:18,181 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-05 09:58:30,634 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 09:58:30,634 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 09:58:30,634 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-05 09:58:30,635 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-05 09:58:30,641 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-05 09:58:30,683 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,684 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,685 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,685 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,686 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,686 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,686 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,687 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,687 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,687 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,688 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,688 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:30,707 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,707 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,707 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,707 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,709 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,709 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,710 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,710 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,710 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,711 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,711 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:30,711 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:31,258 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-05 09:58:36,050 ] 855 livekit.agents - INFO - received job request
[ 2025-08-05 09:58:36,588 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:58:36,590 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:58:37,966 ] 666 root - INFO - Call logging started for call ID: SCL_Qzax7zZZ6FXe
[ 2025-08-05 09:58:41,670 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-05 09:58:41,676 ] 755 root - INFO - Call logging ended for call ID: SCL_Qzax7zZZ6FXe
[ 2025-08-05 09:58:41,678 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-05 09:59:03,434 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-05 09:59:03,434 ] 207 root - INFO - \u26a1 Language detection: 1.241s
[ 2025-08-05 09:59:03,435 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-05 09:59:03,435 ] 513 root - INFO - Enhanced web search query: Chief Minister of India as of 2025 as of 2025
[ 2025-08-05 09:59:03,435 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-05 09:59:03,436 ] 45 root - INFO - \U0001f310 Fast web search: 'Chief Minister of India as of ...'
[ 2025-08-05 09:59:06,421 ] 59 root - INFO - \u26a1 Web search: 2.98s, 2 results
[ 2025-08-05 09:59:06,422 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9872 seconds.
[ 2025-08-05 09:59:06,422 ] 525 root - INFO - \U0001f310 Web search: 4.23s, 2 results
[ 2025-08-05 09:59:28,275 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-05 09:59:28,276 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB Switch Gear Operating Voltage...'
[ 2025-08-05 09:59:30,668 ] 129 root - ERROR - RAG invoke error: (400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Tue, 05 Aug 2025 04:29:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '384', 'x-pinecone-request-id': '55256693734225791', 'x-envoy-upstream-service-time': '42', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}

[ 2025-08-05 09:59:30,668 ] 65 root - ERROR - Vector database search error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Tue, 05 Aug 2025 04:29:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '384', 'x-pinecone-request-id': '55256693734225791', 'x-envoy-upstream-service-time': '42', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]
[ 2025-08-05 09:59:30,669 ] 478 root - ERROR - Vector tool error: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\tools\vector_database_tool.py] line number [45] error message [Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\src\rag\retrieval.py] line number [124] error message [(400)
Reason: Bad Request
HTTP response headers: HTTPHeaderDict({'Date': 'Tue, 05 Aug 2025 04:29:30 GMT', 'Content-Type': 'application/json', 'Content-Length': '102', 'Connection': 'keep-alive', 'x-pinecone-request-latency-ms': '384', 'x-pinecone-request-id': '55256693734225791', 'x-envoy-upstream-service-time': '42', 'server': 'envoy'})
HTTP response body: {"code":3,"message":"Vector dimension 768 does not match the dimension of the index 384","details":[]}
]]
[ 2025-08-05 09:59:55,598 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-05 10:00:19,659 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
