
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { HelpCircle, MessageCircle, Send, Search } from 'lucide-react';

export const HelpSupport = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [feedbackType, setFeedbackType] = useState('');
  const [feedbackMessage, setFeedbackMessage] = useState('');

  const faqs = [
    {
      question: "How do I start a voice conversation?",
      answer: "Click the large microphone button in the center of the screen to start talking with the AI agent."
    },
    {
      question: "Can I switch between languages during a call?",
      answer: "Yes, you can change the language settings at any time using the language selector in the header."
    },
    {
      question: "How do I transfer to a human agent?",
      answer: "Click the 'Transfer to Human' button that appears when the voice agent is active."
    },
    {
      question: "Is my conversation data secure?",
      answer: "Yes, all conversations are GDPR compliant and encrypted. You can view our data handling practices in the compliance section."
    }
  ];

  const filteredFaqs = faqs.filter(faq =>
    faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSubmitFeedback = () => {
    console.log('Submitting feedback:', { feedbackType, feedbackMessage });
    setFeedbackMessage('');
    setFeedbackType('');
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <HelpCircle className="w-5 h-5 mr-2" />
        Help & Support
      </h3>

      <div className="space-y-6">
        {/* FAQ Search */}
        <div>
          <Label className="text-white text-sm font-medium mb-2 block">Search FAQs</Label>
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-3 text-white/60" />
            <Input
              placeholder="Search for help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 bg-white/10 border-white/30 text-white placeholder:text-white/60"
            />
          </div>
        </div>

        {/* FAQ List */}
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {filteredFaqs.map((faq, index) => (
            <details key={index} className="bg-white/10 rounded-lg p-3">
              <summary className="text-white text-sm font-medium cursor-pointer">
                {faq.question}
              </summary>
              <p className="text-white/80 text-sm mt-2">{faq.answer}</p>
            </details>
          ))}
        </div>

        {/* Support Chat */}
        <div>
          <Button
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            onClick={() => console.log('Opening support chat')}
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Start Live Support Chat
          </Button>
        </div>

        {/* Feedback Form */}
        <div>
          <Label className="text-white text-sm font-medium mb-2 block">Send Feedback</Label>
          <div className="space-y-3">
            <Select value={feedbackType} onValueChange={setFeedbackType}>
              <SelectTrigger className="bg-white/10 border-white/30 text-white">
                <SelectValue placeholder="Feedback type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bug">Bug Report</SelectItem>
                <SelectItem value="feature">Feature Request</SelectItem>
                <SelectItem value="improvement">Improvement Suggestion</SelectItem>
                <SelectItem value="general">General Feedback</SelectItem>
              </SelectContent>
            </Select>
            
            <Textarea
              placeholder="Tell us about your experience..."
              value={feedbackMessage}
              onChange={(e) => setFeedbackMessage(e.target.value)}
              className="bg-white/10 border-white/30 text-white placeholder:text-white/60"
              rows={3}
            />
            
            <Button
              onClick={handleSubmitFeedback}
              disabled={!feedbackType || !feedbackMessage}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              <Send className="w-4 h-4 mr-2" />
              Send Feedback
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
};
