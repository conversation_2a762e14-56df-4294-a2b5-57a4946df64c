import os
import sys
import time
import signal
import socket
import subprocess
from pathlib import Path

BASE_DIR = Path(__file__).resolve().parent

def ensure_single_instance(lock_port: int = 53981):
    """Prevents multiple instances from running simultaneously.

    Uses a localhost TCP port bind as a cross-platform lock. If binding fails,
    another instance is already running, so we exit quietly.
    """
    lock_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    lock_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
    try:
        lock_socket.bind(("127.0.0.1", lock_port))
    except OSError:
        print("starttwo: another instance detected; exiting.")
        sys.exit(0)
    lock_socket.listen(1)
    return lock_socket

def start_fastapi() -> subprocess.Popen:
    port = str(int(os.getenv("PORT", "8000")))
    # Use the same interpreter to ensure correct environment
    cmd = [sys.executable, "-m", "uvicorn", "app:app", "--host", "0.0.0.0", "--port", port, "--workers", "1"]
    return subprocess.Popen(cmd, cwd=str(BASE_DIR))

def start_agent() -> subprocess.Popen:
    cmd = [sys.executable, str(BASE_DIR / "langgraphagent.py"), "start"]
    return subprocess.Popen(cmd, cwd=str(BASE_DIR))

def terminate_process(process: subprocess.Popen, name: str):
    if process and process.poll() is None:
        try:
            process.terminate()
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                process.kill()
        except Exception:
            pass

def main():
    lock_socket = ensure_single_instance()

    api_proc = start_fastapi()
    agent_proc = start_agent()

    def handle_signal(signum, frame):
        terminate_process(agent_proc, "agent")
        terminate_process(api_proc, "uvicorn")
        sys.exit(0)

    signal.signal(signal.SIGINT, handle_signal)
    signal.signal(signal.SIGTERM, handle_signal)

    try:
        while True:
            if api_proc.poll() is not None or agent_proc.poll() is not None:
                break
            time.sleep(1)
    finally:
        terminate_process(agent_proc, "agent")
        terminate_process(api_proc, "uvicorn")
        try:
            lock_socket.close()
        except Exception:
            pass

if __name__ == "__main__":
    main()