
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Bell, 
  Settings, 
  User, 
  LogOut, 
  Activity, 
  Shield, 
  Database,
  Clock,
  AlertTriangle,
  Crown,
  Users as UsersIcon
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface AdminHeaderProps {
  onLogout: () => void;
  userRole: 'super_admin' | 'admin' | 'viewer';
}

export const AdminHeader = ({ onLogout, userRole }: AdminHeaderProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);

  const notifications = [
    { id: 1, type: 'info', message: 'Real-time processing pipeline active', time: '1 min ago' },
    { id: 2, type: 'success', message: 'Privacy compliance check passed', time: '5 min ago' },
    { id: 3, type: 'warning', message: 'High response time detected in EU region', time: '15 min ago' },
    { id: 4, type: 'info', message: 'New user added to system', time: '1 hour ago' }
  ];

  const getRoleIcon = () => {
    switch (userRole) {
      case 'super_admin': return <Crown className="w-4 h-4 text-yellow-400" />;
      case 'admin': return <Shield className="w-4 h-4 text-blue-400" />;
      case 'viewer': return <UsersIcon className="w-4 h-4 text-green-400" />;
    }
  };

  const getRoleColor = () => {
    switch (userRole) {
      case 'super_admin': return 'text-yellow-400 border-yellow-400';
      case 'admin': return 'text-blue-400 border-blue-400';
      case 'viewer': return 'text-green-400 border-green-400';
    }
  };

  return (
    <header className="bg-gray-900/90 backdrop-blur-sm border-b border-gray-700">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <img 
            src="/lovable-uploads/649f40b2-e2bb-4999-95a9-76ba7fc7c5c6.png" 
            alt="Ll-aisolutions Logo" 
            className="w-10 h-10"
          />
          <div>
            <h1 className="text-xl font-semibold text-white">Ll-aisolutions Admin Dashboard</h1>
            <p className="text-gray-400 text-sm">Enterprise AI Voice Agent Management Platform</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* User Role Badge */}
          <Badge variant="outline" className={getRoleColor()}>
            {getRoleIcon()}
            <span className="ml-1">{userRole.replace('_', ' ').toUpperCase()}</span>
          </Badge>

          {/* System Status */}
          <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-300">All Systems Operational</span>
            <Clock className="w-3 h-3 text-green-300" />
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNotifications(!showNotifications)}
              className="text-white hover:bg-gray-800 relative"
            >
              <Bell className="w-5 h-5" />
              {notifications.length > 0 && (
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full p-0 flex items-center justify-center">
                  {notifications.length}
                </Badge>
              )}
            </Button>
            
            {showNotifications && (
              <Card className="absolute right-0 top-12 w-80 bg-gray-800/95 backdrop-blur-sm border-gray-700 z-50">
                <div className="p-4">
                  <h3 className="text-white font-medium mb-3">System Notifications</h3>
                  <div className="space-y-2">
                    {notifications.map((notification) => (
                      <div key={notification.id} className="flex items-start space-x-3 p-2 rounded-lg bg-gray-900/50 hover:bg-gray-900/70 transition-colors">
                        {notification.type === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-400 mt-1" />}
                        {notification.type === 'info' && <Activity className="w-4 h-4 text-blue-400 mt-1" />}
                        {notification.type === 'success' && <Shield className="w-4 h-4 text-green-400 mt-1" />}
                        <div className="flex-1">
                          <p className="text-white text-sm">{notification.message}</p>
                          <p className="text-gray-400 text-xs">{notification.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Profile */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowProfile(!showProfile)}
              className="text-white hover:bg-gray-800"
            >
              <User className="w-5 h-5" />
            </Button>
            
            {showProfile && (
              <Card className="absolute right-0 top-12 w-64 bg-gray-800/95 backdrop-blur-sm border-gray-700 z-50">
                <div className="p-2">
                  <div className="px-3 py-2 border-b border-gray-700">
                    <div className="flex items-center space-x-2 mb-1">
                      {getRoleIcon()}
                      <p className="text-white font-medium">
                        {userRole === 'super_admin' ? 'Anup Raj' : 'Admin User'}
                      </p>
                    </div>
                    <p className="text-gray-300 text-sm">
                      {userRole === 'super_admin' ? '<EMAIL>' : '<EMAIL>'}
                    </p>
                    <Badge variant="outline" className={`${getRoleColor()} text-xs mt-1`}>
                      {userRole.replace('_', ' ').toUpperCase()}
                    </Badge>
                  </div>
                  <div className="py-2">
                    <Button variant="ghost" size="sm" className="w-full justify-start text-gray-300 hover:bg-gray-700 hover:text-white">
                      <Settings className="w-4 h-4 mr-2" />
                      Account Settings
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start text-red-400 hover:bg-red-500/10 hover:text-red-300"
                      onClick={onLogout}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Secure Logout
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>

          <Link to="/">
            <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white">
              Voice Interface
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
};
