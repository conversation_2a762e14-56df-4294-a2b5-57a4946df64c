
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Timer, Mic, MicOff, Phone, ThumbsUp, ThumbsDown } from 'lucide-react';

export const CallManagement = () => {
  const [callDuration, setCallDuration] = useState(0);
  const [isRecording, setIsRecording] = useState(true);
  const [transferReason, setTransferReason] = useState('');
  const [showFeedback, setShowFeedback] = useState(false);
  const [feedback, setFeedback] = useState<'positive' | 'negative' | null>(null);
  const [feedbackComment, setFeedbackComment] = useState('');

  // Simulate call timer
  useEffect(() => {
    const interval = setInterval(() => {
      setCallDuration(prev => prev + 1);
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleTransfer = () => {
    console.log('Transferring call with reason:', transferReason);
    setShowFeedback(true);
  };

  const submitFeedback = () => {
    console.log('Feedback submitted:', { feedback, feedbackComment });
    setShowFeedback(false);
    setFeedback(null);
    setFeedbackComment('');
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Phone className="w-5 h-5 mr-2" />
        Call Management
      </h3>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Call Timer & Recording */}
        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-white/10 rounded-lg">
            <div className="flex items-center space-x-3">
              <Timer className="w-5 h-5 text-blue-400" />
              <div>
                <p className="text-white font-medium">Call Duration</p>
                <p className="text-2xl text-white font-bold">{formatTime(callDuration)}</p>
              </div>
            </div>
            <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          </div>

          <div className="flex items-center justify-between p-4 bg-white/10 rounded-lg">
            <div className="flex items-center space-x-3">
              {isRecording ? (
                <Mic className="w-5 h-5 text-red-400" />
              ) : (
                <MicOff className="w-5 h-5 text-gray-400" />
              )}
              <div>
                <p className="text-white font-medium">Call Recording</p>
                <p className="text-white/70 text-sm">
                  {isRecording ? 'Recording active' : 'Recording stopped'}
                </p>
              </div>
            </div>
            <Switch
              checked={isRecording}
              onCheckedChange={setIsRecording}
            />
          </div>
        </div>

        {/* Transfer Controls */}
        <div className="space-y-4">
          <div>
            <label className="text-white text-sm font-medium mb-2 block">
              Transfer Reason (Optional)
            </label>
            <Textarea
              placeholder="Why is this call being transferred to a human agent?"
              value={transferReason}
              onChange={(e) => setTransferReason(e.target.value)}
              className="bg-white/10 border-white/30 text-white placeholder:text-white/60"
              rows={3}
            />
          </div>
          
          <Button
            onClick={handleTransfer}
            className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white"
          >
            <Phone className="w-4 h-4 mr-2" />
            Transfer to Human Agent
          </Button>
        </div>
      </div>

      {/* Post-Call Feedback */}
      {showFeedback && (
        <div className="mt-6 p-4 bg-white/10 rounded-lg border border-white/20">
          <h4 className="text-white font-medium mb-4">How was your experience?</h4>
          
          <div className="flex justify-center space-x-4 mb-4">
            <Button
              variant={feedback === 'positive' ? 'default' : 'outline'}
              onClick={() => setFeedback('positive')}
              className={`${
                feedback === 'positive' 
                  ? 'bg-green-600 text-white' 
                  : 'border-white/30 text-white hover:bg-white/20'
              }`}
            >
              <ThumbsUp className="w-4 h-4 mr-2" />
              Positive
            </Button>
            <Button
              variant={feedback === 'negative' ? 'default' : 'outline'}
              onClick={() => setFeedback('negative')}
              className={`${
                feedback === 'negative' 
                  ? 'bg-red-600 text-white' 
                  : 'border-white/30 text-white hover:bg-white/20'
              }`}
            >
              <ThumbsDown className="w-4 h-4 mr-2" />
              Negative
            </Button>
          </div>

          <Textarea
            placeholder="Optional: Share your thoughts about this conversation..."
            value={feedbackComment}
            onChange={(e) => setFeedbackComment(e.target.value)}
            className="bg-white/10 border-white/30 text-white placeholder:text-white/60 mb-4"
            rows={2}
          />

          <Button
            onClick={submitFeedback}
            disabled={!feedback}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white"
          >
            Submit Feedback
          </Button>
        </div>
      )}
    </Card>
  );
};
