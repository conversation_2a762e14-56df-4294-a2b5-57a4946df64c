
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Bell, 
  Settings, 
  User, 
  LogOut, 
  Activity, 
  Shield, 
  Database,
  Clock,
  AlertTriangle
} from 'lucide-react';
import { Link } from 'react-router-dom';

interface AdminHeaderProps {
  onLogout: () => void;
}

export const AdminHeader = ({ onLogout }: AdminHeaderProps) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);

  const notifications = [
    { id: 1, type: 'warning', message: 'High response time detected', time: '2 min ago' },
    { id: 2, type: 'info', message: 'System update available', time: '1 hour ago' },
    { id: 3, type: 'error', message: 'Failed authentication attempt', time: '3 hours ago' }
  ];

  return (
    <header className="bg-gray-900/50 backdrop-blur-sm border-b border-gray-700">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold">H</span>
          </div>
          <div>
            <h1 className="text-xl font-semibold text-white">Ll-aisolutions Admin</h1>
            <p className="text-gray-400 text-sm">AI Voice Agent Management Platform</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* System Status */}
          <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-green-300">System Operational</span>
            <Clock className="w-3 h-3 text-green-300" />
          </div>

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowNotifications(!showNotifications)}
              className="text-white hover:bg-gray-800 relative"
            >
              <Bell className="w-5 h-5" />
              {notifications.length > 0 && (
                <Badge className="absolute -top-1 -right-1 bg-red-500 text-white text-xs w-5 h-5 rounded-full p-0 flex items-center justify-center">
                  {notifications.length}
                </Badge>
              )}
            </Button>
            
            {showNotifications && (
              <Card className="absolute right-0 top-12 w-80 bg-gray-800 border-gray-700 z-50">
                <div className="p-4">
                  <h3 className="text-white font-medium mb-3">Notifications</h3>
                  <div className="space-y-2">
                    {notifications.map((notification) => (
                      <div key={notification.id} className="flex items-start space-x-3 p-2 rounded-lg bg-gray-900/50">
                        {notification.type === 'warning' && <AlertTriangle className="w-4 h-4 text-yellow-400 mt-1" />}
                        {notification.type === 'info' && <Activity className="w-4 h-4 text-blue-400 mt-1" />}
                        {notification.type === 'error' && <Shield className="w-4 h-4 text-red-400 mt-1" />}
                        <div className="flex-1">
                          <p className="text-white text-sm">{notification.message}</p>
                          <p className="text-gray-400 text-xs">{notification.time}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </Card>
            )}
          </div>

          {/* Profile */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowProfile(!showProfile)}
              className="text-white hover:bg-gray-800"
            >
              <User className="w-5 h-5" />
            </Button>
            
            {showProfile && (
              <Card className="absolute right-0 top-12 w-48 bg-gray-800 border-gray-700 z-50">
                <div className="p-2">
                  <div className="px-3 py-2 border-b border-gray-700">
                    <p className="text-white font-medium">Admin User</p>
                    <p className="text-gray-400 text-sm"><EMAIL></p>
                  </div>
                  <div className="py-2">
                    <Button variant="ghost" size="sm" className="w-full justify-start text-gray-300">
                      <Settings className="w-4 h-4 mr-2" />
                      Settings
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="w-full justify-start text-gray-300"
                      onClick={onLogout}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Logout
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>

          <Link to="/">
            <Button variant="outline" size="sm" className="w-full text-left justify-start border-0 bg-gradient-to-r from-pink-500 to-purple-600 text-white hover:from-pink-600 hover:to-purple-700 hover:shadow-lg transition-all duration-200 h-auto p-3">
              Voice Interface
            </Button>
          </Link>
        </div>
      </div>
    </header>
  );
};
