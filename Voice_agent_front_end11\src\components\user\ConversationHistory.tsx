
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Download, Play, Pause, User, Bot, Clock, FileText } from 'lucide-react';

interface Conversation {
  id: string;
  timestamp: string;
  duration: string;
  type: 'ai' | 'human';
  status: 'completed' | 'transferred' | 'ongoing';
  transcript: string;
  summary?: string;
  tags?: string[];
}

export const ConversationHistory = () => {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [playingAudio, setPlayingAudio] = useState<string | null>(null);

  const conversations: Conversation[] = [
    {
      id: '1',
      timestamp: '2024-01-20 14:30',
      duration: '5:23',
      type: 'ai',
      status: 'completed',
      transcript: 'User: Hello, I need help with my account.\nAI: I\'d be happy to help you with your account. What specific issue are you experiencing?',
      summary: 'Account assistance inquiry',
      tags: ['account', 'support']
    },
    {
      id: '2',
      timestamp: '2024-01-20 13:15',
      duration: '8:45',
      type: 'human',
      status: 'transferred',
      transcript: 'User: I have a complex billing issue.\nAI: I understand this is a complex billing matter. Let me transfer you to a human specialist.\nHuman Agent: Hello, I\'m here to help with your billing issue.',
      summary: 'Billing issue - transferred to human',
      tags: ['billing', 'transferred', 'complex']
    }
  ];

  const handleExport = (format: 'pdf' | 'text') => {
    console.log(`Exporting conversation history as ${format}`);
    // Implementation for export functionality
  };

  const toggleAudioPlayback = (conversationId: string) => {
    if (playingAudio === conversationId) {
      setPlayingAudio(null);
    } else {
      setPlayingAudio(conversationId);
    }
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-white text-lg font-semibold flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Conversation History
        </h3>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('pdf')}
            className="border-white/30 text-white hover:bg-white/20"
          >
            <Download className="w-4 h-4 mr-1" />
            PDF
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('text')}
            className="border-white/30 text-white hover:bg-white/20"
          >
            <FileText className="w-4 h-4 mr-1" />
            Text
          </Button>
        </div>
      </div>

      <ScrollArea className="h-80">
        <div className="space-y-3">
          {conversations.map((conversation) => (
            <div
              key={conversation.id}
              className="p-4 bg-white/10 rounded-lg cursor-pointer hover:bg-white/20 transition-colors"
              onClick={() => setSelectedConversation(conversation.id)}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={conversation.type === 'ai' ? 'default' : 'secondary'}
                    className={`${
                      conversation.type === 'ai' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-green-500 text-white'
                    }`}
                  >
                    {conversation.type === 'ai' ? (
                      <><Bot className="w-3 h-3 mr-1" /> AI Agent</>
                    ) : (
                      <><User className="w-3 h-3 mr-1" /> Human Agent</>
                    )}
                  </Badge>
                  <span className="text-sm text-white/70">{conversation.timestamp}</span>
                  <span className="text-sm text-white/70">{conversation.duration}</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleAudioPlayback(conversation.id);
                  }}
                  className="text-white hover:bg-white/20"
                >
                  {playingAudio === conversation.id ? (
                    <Pause className="w-4 h-4" />
                  ) : (
                    <Play className="w-4 h-4" />
                  )}
                </Button>
              </div>
              
              <p className="text-sm text-white/80 mb-2">{conversation.summary}</p>
              
              {conversation.tags && (
                <div className="flex flex-wrap gap-1">
                  {conversation.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs border-white/30 text-white">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {selectedConversation === conversation.id && (
                <div className="mt-3 p-3 bg-white/10 rounded text-sm text-white/90">
                  <strong>Full Transcript:</strong>
                  <pre className="whitespace-pre-wrap mt-1">{conversation.transcript}</pre>
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </Card>
  );
};
