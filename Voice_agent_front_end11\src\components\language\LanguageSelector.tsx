
import React from 'react';
import { Button } from '@/components/ui/button';
import { Globe } from 'lucide-react';
import { Language } from '@/pages/Index';

interface LanguageSelectorProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
}

export const LanguageSelector = ({ currentLanguage, onLanguageChange }: LanguageSelectorProps) => {
  const languages = [
    { code: 'en' as Language, name: 'English', flag: '🇺🇸' },
    { code: 'de' as Language, name: 'Deutsch', flag: '🇩🇪' },
    { code: 'tr' as Language, name: 'Türkçe', flag: '🇹🇷' }
  ];

  const currentLang = languages.find(lang => lang.code === currentLanguage);

  return (
    <div className="flex items-center space-x-2">
      <Globe className="w-4 h-4 text-white" />
      <div className="flex space-x-1">
        {languages.map((language) => (
          <Button
            key={language.code}
            variant={currentLanguage === language.code ? "default" : "ghost"}
            size="sm"
            onClick={() => onLanguageChange(language.code)}
            className={`text-xs ${
              currentLanguage === language.code
                ? 'bg-white text-purple-600'
                : 'text-white hover:bg-white/20'
            }`}
          >
            {language.flag} {language.name}
          </Button>
        ))}
      </div>
    </div>
  );
};
