
import React from 'react';
import { VoiceStatus } from '@/pages/Index';
import { Loader2, Mic, Volume2, Clock } from 'lucide-react';

interface StatusIndicatorProps {
  status: VoiceStatus;
}

export const StatusIndicator = ({ status }: StatusIndicatorProps) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'listening':
        return {
          icon: <Mic className="w-6 h-6" />,
          text: 'Listening...',
          color: 'text-blue-300',
          bgColor: 'bg-blue-500/20',
          borderColor: 'border-blue-400'
        };
      case 'processing':
        return {
          icon: <Loader2 className="w-6 h-6 animate-spin" />,
          text: 'Processing...',
          color: 'text-yellow-300',
          bgColor: 'bg-yellow-500/20',
          borderColor: 'border-yellow-400'
        };
      case 'speaking':
        return {
          icon: <Volume2 className="w-6 h-6" />,
          text: 'Speaking...',
          color: 'text-green-300',
          bgColor: 'bg-green-500/20',
          borderColor: 'border-green-400'
        };
      default:
        return {
          icon: <Clock className="w-6 h-6" />,
          text: 'Ready',
          color: 'text-gray-300',
          bgColor: 'bg-gray-500/20',
          borderColor: 'border-gray-400'
        };
    }
  };

  const config = getStatusConfig();

  return (
    <div className={`inline-flex items-center space-x-3 px-6 py-3 rounded-full border ${config.bgColor} ${config.borderColor} backdrop-blur-sm`}>
      <div className={config.color}>
        {config.icon}
      </div>
      <span className={`font-medium ${config.color}`}>
        {config.text}
      </span>
    </div>
  );
};
