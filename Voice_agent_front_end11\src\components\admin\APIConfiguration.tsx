
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  Save, 
  TestTube, 
  Eye, 
  EyeOff,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface APIEndpoint {
  name: string;
  url: string;
  apiKey: string;
  enabled: boolean;
  provider: string;
}

export const APIConfiguration = () => {
  const [showApiKeys, setShowApiKeys] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, 'success' | 'error' | 'testing'>>({});
  
  const [endpoints, setEndpoints] = useState<Record<string, APIEndpoint>>({
    stt: {
      name: 'Speech-to-Text',
      url: 'https://api.openai.com/v1/audio/transcriptions',
      apiKey: '',
      enabled: true,
      provider: 'openai'
    },
    llm: {
      name: 'Language Model',
      url: 'https://api.groq.com/openai/v1/chat/completions',
      apiKey: '',
      enabled: true,
      provider: 'groq'
    },
    tts: {
      name: 'Text-to-Speech',
      url: "https://play.cartesia.ai/text-to-speech",
      apiKey: '',
      enabled: true,
      provider: 'cartesia'
    },
    emotion: {
      name: 'Emotion Detection',
      url: 'https://api.hf.co/models/cardiffnlp/twitter-roberta-base-emotion',
      apiKey: '',
      enabled: false,
      provider: 'huggingface'
    }
  });

  const providers = {
    openai: { name: 'OpenAI', models: ['whisper-1', 'gpt-4', 'gpt-3.5-turbo'] },
    groq: { name: 'Groq', models: ['llama2-70b-4096', 'mixtral-8x7b-32768'] },
    elevenlabs: { name: 'ElevenLabs', models: ['eleven_monolingual_v1', 'eleven_multilingual_v2'] },
    huggingface: { name: 'Hugging Face', models: ['emotion-detection', 'sentiment-analysis'] },
    ollama: { name: 'Ollama (Local)', models: ['llama2', 'mistral', 'codellama'] }
  };

  const updateEndpoint = (key: string, field: string, value: any) => {
    setEndpoints(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        [field]: value
      }
    }));
  };

  const testEndpoint = async (key: string) => {
    setTestResults(prev => ({ ...prev, [key]: 'testing' }));
    
    try {
      // Simulate API test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock test result
      const success = Math.random() > 0.3; // 70% success rate for demo
      setTestResults(prev => ({ 
        ...prev, 
        [key]: success ? 'success' : 'error' 
      }));
    } catch (error) {
      setTestResults(prev => ({ ...prev, [key]: 'error' }));
    }
  };

  const saveConfiguration = () => {
    // Save to localStorage or backend
    localStorage.setItem('voiceEngineConfig', JSON.stringify(endpoints));
    console.log('Configuration saved:', endpoints);
  };

  const getTestIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'testing':
        return <TestTube className="w-4 h-4 text-yellow-500 animate-spin" />;
      default:
        return <TestTube className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white flex items-center">
          <Settings className="w-6 h-6 mr-2" />
          Voice Engine API Configuration
        </h2>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              checked={showApiKeys}
              onCheckedChange={setShowApiKeys}
            />
            <Label className="text-white">Show API Keys</Label>
            {showApiKeys ? (
              <Eye className="w-4 h-4 text-white" />
            ) : (
              <EyeOff className="w-4 h-4 text-white" />
            )}
          </div>
          
          <Button onClick={saveConfiguration} className="bg-green-600 hover:bg-green-700">
            <Save className="w-4 h-4 mr-2" />
            Save Configuration
          </Button>
        </div>
      </div>

      {/* API Endpoints Configuration */}
      <div className="grid gap-6">
        {Object.entries(endpoints).map(([key, endpoint]) => (
          <Card key={key} className="p-6 bg-gray-800/50 border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold text-white">{endpoint.name}</h3>
                <Badge variant={endpoint.enabled ? "default" : "secondary"}>
                  {endpoint.enabled ? "Enabled" : "Disabled"}
                </Badge>
                <Badge variant="outline" className="text-gray-300">
                  {providers[endpoint.provider as keyof typeof providers]?.name}
                </Badge>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  checked={endpoint.enabled}
                  onCheckedChange={(enabled) => updateEndpoint(key, 'enabled', enabled)}
                />
                <Button
                  onClick={() => testEndpoint(key)}
                  variant="outline"
                  size="sm"
                  disabled={testResults[key] === 'testing' || !endpoint.enabled}
                  className="text-white border-gray-600"
                >
                  {getTestIcon(testResults[key])}
                  Test
                </Button>
              </div>
            </div>

            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-300">Provider</Label>
                <Select
                  value={endpoint.provider}
                  onValueChange={(provider) => updateEndpoint(key, 'provider', provider)}
                >
                  <SelectTrigger className="bg-gray-900 border-gray-700 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(providers).map(([value, provider]) => (
                      <SelectItem key={value} value={value}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-gray-300">API Endpoint URL</Label>
                <Input
                  value={endpoint.url}
                  onChange={(e) => updateEndpoint(key, 'url', e.target.value)}
                  placeholder="Enter API endpoint URL"
                  className="bg-gray-900 border-gray-700 text-white"
                />
              </div>

              <div className="md:col-span-2">
                <Label className="text-gray-300">API Key</Label>
                <Input
                  type={showApiKeys ? "text" : "password"}
                  value={endpoint.apiKey}
                  onChange={(e) => updateEndpoint(key, 'apiKey', e.target.value)}
                  placeholder="Enter API key"
                  className="bg-gray-900 border-gray-700 text-white"
                />
              </div>
            </div>

            {/* Test Results */}
            {testResults[key] && (
              <div className={`mt-4 p-3 rounded-lg border ${
                testResults[key] === 'success' 
                  ? 'bg-green-500/10 border-green-500/30 text-green-300'
                  : testResults[key] === 'error'
                  ? 'bg-red-500/10 border-red-500/30 text-red-300'
                  : 'bg-yellow-500/10 border-yellow-500/30 text-yellow-300'
              }`}>
                {testResults[key] === 'success' && "✅ API endpoint is responding correctly"}
                {testResults[key] === 'error' && "❌ API endpoint test failed - check URL and API key"}
                {testResults[key] === 'testing' && "🔄 Testing API endpoint..."}
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* Open Source Alternatives */}
      <Card className="p-6 bg-blue-900/20 border-blue-500/30">
        <h3 className="text-lg font-semibold text-white mb-4">💡 Open Source & Free Alternatives</h3>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="text-blue-300 font-medium mb-2">Speech-to-Text</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• OpenAI Whisper (Local deployment)</li>
              <li>• Web Speech API (Browser native)</li>
              <li>• Mozilla DeepSpeech</li>
            </ul>
          </div>
          <div>
            <h4 className="text-blue-300 font-medium mb-2">Language Models</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• Ollama (Local LLMs)</li>
              <li>• Groq (Free tier)</li>
              <li>• Together AI (Free tier)</li>
            </ul>
          </div>
          <div>
            <h4 className="text-blue-300 font-medium mb-2">Text-to-Speech</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• Web Speech Synthesis API</li>
              <li>• ElevenLabs (Free tier)</li>
              <li>• Coqui TTS (Open source)</li>
            </ul>
          </div>
          <div>
            <h4 className="text-blue-300 font-medium mb-2">Emotion Detection</h4>
            <ul className="text-gray-300 space-y-1">
              <li>• Hugging Face Transformers.js</li>
              <li>• Browser-based ML models</li>
              <li>• Keyword-based detection</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
};
