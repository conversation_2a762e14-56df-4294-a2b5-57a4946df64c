[ 2025-08-04 13:33:34,443 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 13:33:34,444 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 13:33:34,905 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4604 seconds.
[ 2025-08-04 13:33:39,001 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-04 13:33:50,101 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 13:33:50,101 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 13:33:50,102 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 13:35:01,206 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 13:35:01,206 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 13:35:01,207 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 13:35:01,207 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 13:35:01,214 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 13:35:01,269 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,270 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,270 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,270 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,272 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,273 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,273 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,274 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,274 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,275 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,275 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,276 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:35:01,293 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,295 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,296 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,296 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,297 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,297 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,297 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,297 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,298 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,298 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,298 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,299 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:35:01,857 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-04 13:56:10,245 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 13:56:10,377 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:56:10,386 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:56:11,669 ] 666 root - INFO - Call logging started for call ID: SCL_GCRTPpA5hf3M
[ 2025-08-04 13:56:14,262 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 13:56:14,269 ] 755 root - INFO - Call logging ended for call ID: SCL_GCRTPpA5hf3M
[ 2025-08-04 13:56:14,270 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 13:56:33,607 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-04 13:56:33,607 ] 207 root - INFO - \u26a1 Language detection: 0.275s
[ 2025-08-04 13:56:33,608 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 13:56:33,608 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:56:33,609 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-04 13:56:36,626 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:56:36,627 ] 159 root - INFO - \u26a1 New response generated in 3.02s
[ 2025-08-04 13:56:36,627 ] 51 root - INFO - Vector DB search completed in 3.02s
[ 2025-08-04 13:56:36,628 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:56:36,628 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.0205 seconds.
[ 2025-08-04 13:56:36,628 ] 457 root - INFO - \u2705 Vector search: 3.30s
[ 2025-08-04 13:56:37,552 ] 513 root - INFO - Enhanced web search query: current weather as of 2025 as of 2025
[ 2025-08-04 13:56:37,556 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:56:37,558 ] 45 root - INFO - \U0001f310 Fast web search: 'current weather as of 2025 as ...'
[ 2025-08-04 13:56:38,715 ] 513 root - INFO - Enhanced web search query: weather as of 2025 as of 2025
[ 2025-08-04 13:56:38,717 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:56:38,717 ] 45 root - INFO - \U0001f310 Fast web search: 'weather as of 2025 as of 2025...'
[ 2025-08-04 13:56:40,664 ] 59 root - INFO - \u26a1 Web search: 3.11s, 2 results
[ 2025-08-04 13:56:40,665 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.1087 seconds.
[ 2025-08-04 13:56:40,666 ] 525 root - INFO - \U0001f310 Web search: 3.11s, 2 results
[ 2025-08-04 13:56:41,691 ] 59 root - INFO - \u26a1 Web search: 2.97s, 2 results
[ 2025-08-04 13:56:41,693 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9762 seconds.
[ 2025-08-04 13:56:41,694 ] 525 root - INFO - \U0001f310 Web search: 2.98s, 2 results
[ 2025-08-04 13:56:48,245 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:56:48,246 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear...'
[ 2025-08-04 13:56:49,647 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:56:49,648 ] 159 root - INFO - \u26a1 New response generated in 1.40s
[ 2025-08-04 13:56:49,648 ] 51 root - INFO - Vector DB search completed in 1.40s
[ 2025-08-04 13:56:49,650 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:56:49,650 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.4051 seconds.
[ 2025-08-04 13:56:49,653 ] 457 root - INFO - \u2705 Vector search: 1.41s
[ 2025-08-04 13:56:50,146 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:56:50,149 ] 37 root - INFO - \U0001f50d Vector DB search for: 'transformers...'
[ 2025-08-04 13:56:51,268 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:56:51,268 ] 159 root - INFO - \u26a1 New response generated in 1.12s
[ 2025-08-04 13:56:51,270 ] 51 root - INFO - Vector DB search completed in 1.12s
[ 2025-08-04 13:56:51,270 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:56:51,270 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.1241 seconds.
[ 2025-08-04 13:56:51,272 ] 457 root - INFO - \u2705 Vector search: 1.13s
[ 2025-08-04 13:56:51,998 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:56:52,000 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG (Retrieval-Augmented Generation)...'
[ 2025-08-04 13:56:53,009 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:56:53,011 ] 159 root - INFO - \u26a1 New response generated in 1.01s
[ 2025-08-04 13:56:53,011 ] 51 root - INFO - Vector DB search completed in 1.01s
[ 2025-08-04 13:56:53,011 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:56:53,011 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0120 seconds.
[ 2025-08-04 13:56:53,013 ] 457 root - INFO - \u2705 Vector search: 1.02s
[ 2025-08-04 13:56:53,619 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:56:53,619 ] 37 root - INFO - \U0001f50d Vector DB search for: 'NLP (Natural Language Processing)...'
[ 2025-08-04 13:56:54,750 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:56:54,752 ] 159 root - INFO - \u26a1 New response generated in 1.13s
[ 2025-08-04 13:56:54,753 ] 51 root - INFO - Vector DB search completed in 1.13s
[ 2025-08-04 13:56:54,753 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:56:54,755 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.1357 seconds.
[ 2025-08-04 13:56:54,756 ] 457 root - INFO - \u2705 Vector search: 1.14s
[ 2025-08-04 13:56:54,763 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-04 13:57:04,945 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 13:57:13,581 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 13:57:14,925 ] 666 root - INFO - Call logging started for call ID: SCL_7AeGEHhM6yeq
[ 2025-08-04 13:57:15,234 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:57:15,235 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:57:19,625 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 13:57:19,629 ] 755 root - INFO - Call logging ended for call ID: SCL_7AeGEHhM6yeq
[ 2025-08-04 13:57:19,630 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 13:57:27,093 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-04 13:58:41,658 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 13:59:04,202 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-04 14:01:02,125 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 14:01:02,224 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:01:02,226 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:01:03,970 ] 666 root - INFO - Call logging started for call ID: SCL_nbZHxZf3GfHz
[ 2025-08-04 14:01:09,213 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 14:01:09,215 ] 755 root - INFO - Call logging ended for call ID: SCL_nbZHxZf3GfHz
[ 2025-08-04 14:01:09,216 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 14:01:15,571 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 14:01:15,573 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 14:01:15,573 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-04 14:01:15,573 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-04 14:01:15,574 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-04 14:01:15,574 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 14:01:15,575 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0030 seconds.
[ 2025-08-04 14:01:15,575 ] 457 root - INFO - \u2705 Vector search: 0.00s
[ 2025-08-04 14:01:16,477 ] 513 root - INFO - Enhanced web search query: Prime Minister as of 2025 as of 2025
[ 2025-08-04 14:01:16,478 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 14:01:16,479 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister as of 2025 as o...'
[ 2025-08-04 14:01:19,611 ] 59 root - INFO - \u26a1 Web search: 3.13s, 2 results
[ 2025-08-04 14:01:19,611 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.1338 seconds.
[ 2025-08-04 14:01:19,614 ] 525 root - INFO - \U0001f310 Web search: 3.14s, 2 results
[ 2025-08-04 14:01:19,918 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 14:01:19,918 ] 37 root - INFO - \U0001f50d Vector DB search for: 'Prime way...'
[ 2025-08-04 14:01:22,307 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 14:01:22,307 ] 159 root - INFO - \u26a1 New response generated in 2.39s
[ 2025-08-04 14:01:22,307 ] 51 root - INFO - Vector DB search completed in 2.39s
[ 2025-08-04 14:01:22,307 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 14:01:22,308 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.3896 seconds.
[ 2025-08-04 14:01:22,308 ] 457 root - INFO - \u2705 Vector search: 2.39s
[ 2025-08-04 14:01:22,818 ] 513 root - INFO - Enhanced web search query: ABB switchgear as of 2025 as of 2025
[ 2025-08-04 14:01:22,819 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 14:01:22,819 ] 45 root - INFO - \U0001f310 Fast web search: 'ABB switchgear as of 2025 as o...'
[ 2025-08-04 14:01:25,027 ] 59 root - INFO - \u26a1 Web search: 2.21s, 2 results
[ 2025-08-04 14:01:25,027 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2087 seconds.
[ 2025-08-04 14:01:25,028 ] 525 root - INFO - \U0001f310 Web search: 2.21s, 2 results
[ 2025-08-04 14:01:28,257 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 14:01:28,257 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB transformers...'
[ 2025-08-04 14:01:30,061 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 14:01:30,062 ] 159 root - INFO - \u26a1 New response generated in 1.80s
[ 2025-08-04 14:01:30,062 ] 51 root - INFO - Vector DB search completed in 1.80s
[ 2025-08-04 14:01:30,063 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 14:01:30,063 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.8053 seconds.
[ 2025-08-04 14:01:30,064 ] 457 root - INFO - \u2705 Vector search: 1.81s
[ 2025-08-04 14:01:30,065 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-04 14:01:52,219 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 14:02:14,192 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-04 14:16:13,238 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-04 14:16:13,239 ] 560 livekit.agents - INFO - shutting down worker
