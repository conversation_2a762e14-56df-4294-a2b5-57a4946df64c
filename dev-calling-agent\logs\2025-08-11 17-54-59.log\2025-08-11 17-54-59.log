[ 2025-08-11 17:55:03,006 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 17:55:03,018 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,019 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,020 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,021 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,021 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,022 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,025 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,026 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,030 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,031 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,031 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,032 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:03,044 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,044 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,044 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,044 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,048 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:03,592 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 17:55:07,322 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 17:55:08,255 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 17:55:08,326 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:55:08,327 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:55:08,699 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4441 seconds.
[ 2025-08-11 17:55:12,608 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 17:55:22,186 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 17:55:22,186 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 17:55:22,186 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 17:55:23,081 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.83 seconds
[ 2025-08-11 17:55:40,319 ] 301 root - INFO - \U0001f4f1 DTMF received: 1
[ 2025-08-11 17:55:40,320 ] 160 root - INFO - \U0001f30d Language instructions updated to: English
[ 2025-08-11 17:55:40,320 ] 205 root - INFO - \U0001f50a TTS voice updated to: simba-english
[ 2025-08-11 17:55:40,322 ] 214 root - INFO - \U0001f4dd Language instructions updated in session
[ 2025-08-11 17:55:40,323 ] 334 root - INFO - \U0001f30d Language locked to: English (en)
[ 2025-08-11 17:56:23,087 ] 360 root - INFO - Participant disconnected: sip_+916295716352
[ 2025-08-11 17:56:23,088 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 17:56:44,104 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-11 17:57:07,232 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 17:57:07,234 ] 560 livekit.agents - INFO - shutting down worker
