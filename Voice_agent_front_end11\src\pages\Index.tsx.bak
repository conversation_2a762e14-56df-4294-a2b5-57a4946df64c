
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { VoiceEngineController } from '@/components/voice/VoiceEngineController';
import { EnhancedVoiceEngineController } from '@/components/voice/EnhancedVoiceEngineController';
import { LanguageSelector } from '@/components/language/LanguageSelector';
import { VoiceController } from '@/components/voice/VoiceController';
import { ConversationIntelligence } from '@/components/user/ConversationIntelligence';
import { CallManagement } from '@/components/user/CallManagement';
import { RealTimeInteractionFeedback } from '@/components/user/RealTimeInteractionFeedback';
import { AIBehaviorSettings } from '@/components/user/AIBehaviorSettings';
import { VoiceActivityMonitor } from '@/components/user/VoiceActivityMonitor';
import { Settings, Brain, Zap, Activity } from 'lucide-react';

export type Language = 'en' | 'de' | 'tr';
export type VoiceStatus = 'idle' | 'listening' | 'processing' | 'speaking';
export type VoiceType = 'formal' | 'casual';
export type VoiceGender = 'male' | 'female';

const Index = () => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>('en');
  const [voiceStatus, setVoiceStatus] = useState<VoiceStatus>('idle');
  const [useAIAgent, setUseAIAgent] = useState(true);
  
  // Real-time metrics state
  const [realTimeMetrics, setRealTimeMetrics] = useState({
    aiPerformance: 94.7,
    responseTime: 1.2,
    customerSatisfaction: 92,
    costSavings: 78,
    activeCallsToday: 1247,
    successRate: 94.2,
    emotionAccuracy: 87.5,
    privacyCompliance: 100
  });
  
  // AI Behavior Settings
  const [responseStyle, setResponseStyle] = useState<'concise' | 'detailed' | 'empathetic'>('empathetic');
  const [personality, setPersonality] = useState<'professional' | 'friendly' | 'formal' | 'casual'>('friendly');
  const [dialect, setDialect] = useState<'us' | 'uk' | 'au' | 'de-de' | 'de-at' | 'de-ch' | 'tr-tr' | 'tr-cy'>('us');
  
  // Voice Activity Settings
  const [isListening, setIsListening] = useState(false);
  const [noiseSuppressionEnabled, setNoiseSuppressionEnabled] = useState(true);

  // Simulate real-time metric updates
  useEffect(() => {
    const interval = setInterval(() => {
      setRealTimeMetrics(prev => ({
        ...prev,
        aiPerformance: Math.max(90, Math.min(100, prev.aiPerformance + (Math.random() - 0.5) * 2)),
        responseTime: Math.max(0.8, Math.min(3.0, prev.responseTime + (Math.random() - 0.5) * 0.2)),
        customerSatisfaction: Math.max(85, Math.min(100, prev.customerSatisfaction + (Math.random() - 0.5) * 3)),
        activeCallsToday: prev.activeCallsToday + Math.floor(Math.random() * 3),
        successRate: Math.max(90, Math.min(100, prev.successRate + (Math.random() - 0.5) * 1)),
        emotionAccuracy: Math.max(80, Math.min(95, prev.emotionAccuracy + (Math.random() - 0.5) * 2))
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const handleConversationUpdate = (conversation: any) => {
    console.log('New conversation entry:', conversation);
    // Update conversation state, analytics, etc.
  };

  const handleCallTransfer = () => {
    console.log('Transferring to human agent...');
    setUseAIAgent(false);
  };

  const handleToggleAIAgent = () => {
    setUseAIAgent(!useAIAgent);
    setVoiceStatus('idle');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      {/* Navigation */}
      <nav className="bg-gray-900/90 backdrop-blur-sm border-b border-gray-700">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <img 
              src="/lovable-uploads/649f40b2-e2bb-4999-95a9-76ba7fc7c5c6.png" 
              alt="Ll-aisolutions Logo" 
              className="w-10 h-10"
            />
            <span className="text-white text-lg font-semibold">Ll-aisolutions Voice AI</span>
            
            {/* AI Agent Toggle */}
            <div className="flex items-center space-x-2 ml-8">
              <Button
                onClick={handleToggleAIAgent}
                variant={useAIAgent ? "default" : "outline"}
                size="sm"
                className={useAIAgent 
                  ? "bg-green-600 hover:bg-green-700 text-white" 
                  : "text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
                }
              >
                <Brain className="w-4 h-4 mr-2" />
                {useAIAgent ? 'AI Agent Active' : 'Human Mode'}
              </Button>
              
              {useAIAgent && (
                <div className="flex items-center space-x-1">
                  <Zap className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400 text-xs font-medium">FULL DUPLEX</span>
                </div>
              )}
            </div>

            {/* Real-time status indicator */}
            <div className="flex items-center space-x-2 px-3 py-1 bg-green-500/20 rounded-full">
              <Activity className="w-3 h-3 text-green-400 animate-pulse" />
              <span className="text-green-300 text-xs">Live Processing</span>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <LanguageSelector
              currentLanguage={currentLanguage}
              onLanguageChange={setCurrentLanguage}
            />
            
            <Link to="/admin">
              <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700">
                <Settings className="w-4 h-4 mr-2" />
                Admin Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-2 gap-8">
          {/* Left Column - Voice Interface */}
          <div className="space-y-6">
            {useAIAgent ? (
              <EnhancedVoiceEngineController
                language={currentLanguage}
                onConversationUpdate={handleConversationUpdate}
              />
            ) : (
              <VoiceController
                status={voiceStatus}
                onStatusChange={setVoiceStatus}
                onCallTransfer={handleCallTransfer}
              />
            )}
            
            {/* AI Behavior Settings (only show for AI mode) */}
            {useAIAgent && (
              <AIBehaviorSettings
                responseStyle={responseStyle}
                personality={personality}
                dialect={dialect}
                onResponseStyleChange={setResponseStyle}
                onPersonalityChange={setPersonality}
                onDialectChange={setDialect}
              />
            )}
            
            {/* Voice Activity Monitor */}
            <VoiceActivityMonitor
              isListening={isListening}
              noiseSuppressionEnabled={noiseSuppressionEnabled}
              onToggleNoiseSuppression={setNoiseSuppressionEnabled}
            />
          </div>

          {/* Right Column - Intelligence & Management */}
          <div className="space-y-6">
            {/* Real-time Interaction Feedback */}
            <RealTimeInteractionFeedback status={voiceStatus} />
            
            {/* Conversation Intelligence (only for AI mode) */}
            {useAIAgent && <ConversationIntelligence />}
            
            {/* Call Management */}
            <CallManagement />
          </div>
        </div>

        {/* Bottom Section - Real-time Performance Metrics */}
        <div className="mt-12 grid md:grid-cols-4 gap-6">
          <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-6 rounded-xl text-white hover:shadow-xl transition-shadow">
            <h3 className="text-lg font-semibold mb-2">AI Performance</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.aiPerformance.toFixed(1)}%</p>
            <p className="text-blue-100 text-sm">Real-time Accuracy</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-blue-100">Live</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-green-600 to-teal-600 p-6 rounded-xl text-white hover:shadow-xl transition-shadow">
            <h3 className="text-lg font-semibold mb-2">Response Time</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.responseTime.toFixed(1)}s</p>
            <p className="text-green-100 text-sm">Average Latency</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-100">Live</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-orange-600 to-red-600 p-6 rounded-xl text-white hover:shadow-xl transition-shadow">
            <h3 className="text-lg font-semibold mb-2">Customer Satisfaction</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.customerSatisfaction}%</p>
            <p className="text-orange-100 text-sm">Real-time Feedback</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-orange-100">Live</span>
            </div>
          </div>
          
          <div className="bg-gradient-to-br from-purple-600 to-pink-600 p-6 rounded-xl text-white hover:shadow-xl transition-shadow">
            <h3 className="text-lg font-semibold mb-2">Privacy Compliance</h3>
            <p className="text-3xl font-bold">{realTimeMetrics.privacyCompliance}%</p>
            <p className="text-purple-100 text-sm">GDPR + EU AI Act</p>
            <div className="mt-2 flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-xs text-purple-100">Protected</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
