
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { backendService } from '@/services/api/BackendService';
import { 
  Settings, 
  Key, 
  TestTube, 
  CheckCircle, 
  AlertCircle, 
  Eye, 
  EyeOff,
  Mic,
  Volume2,
  Brain,
  Heart,
  Zap,
  Save,
  Server,
  Database,
  Cloud,
  Shield
} from 'lucide-react';

interface APIConfig {
  service: string;
  provider: string;
  apiKey: string;
  endpoint: string;
  status: 'connected' | 'disconnected' | 'testing' | 'error';
  isEnabled: boolean;
  showKey: boolean;
  category: 'ai' | 'backend' | 'infrastructure';
  model?: string;
}

interface BackendModel {
  value: string;
  label: string;
  provider: string;
  endpoint: string;
}

interface AvailableModels {
  stt: {
    models: BackendModel[];
    default: string;
  };
  llm: {
    models: BackendModel[];
    default: string;
  };
  tts: {
    models: BackendModel[];
    default: string;
  };
}

export const EnhancedAPIConfiguration = () => {
  const { toast } = useToast();
  const [availableModels, setAvailableModels] = useState<AvailableModels | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [configs, setConfigs] = useState<{ [key: string]: APIConfig }>({
    // AI Services
    stt: {
      service: 'Speech-to-Text',
      provider: 'groq',
      apiKey: '',
      endpoint: '',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai',
      model: ''
    },
    tts: {
      service: 'Text-to-Speech', 
      provider: 'speechify',
      apiKey: '',
      endpoint: '',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai',
      model: ''
    },
    llm: {
      service: 'Language Model',
      provider: 'groq',
      apiKey: '',
      endpoint: '',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'ai',
      model: ''
    },
    emotion: {
      service: 'Emotion AI',
      provider: 'dummy',
      apiKey: '',
      endpoint: 'N/A',
      status: 'disconnected',
      isEnabled: false,
      showKey: false,
      category: 'ai',
      model: 'Coming Soon'
    },
    orchestration: {
      service: 'Conversation Orchestration',
      provider: 'dummy',
      apiKey: '',
      endpoint: 'N/A',
      status: 'disconnected',
      isEnabled: false,
      showKey: false,
      category: 'ai',
      model: 'Coming Soon'
    },
    // Backend Services
    database: {
      service: 'Database Service',
      provider: 'postgresql',
      apiKey: '',
      endpoint: 'postgresql://localhost:5432/voiceengine',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    redis: {
      service: 'Redis Cache',
      provider: 'redis',
      apiKey: '',
      endpoint: 'redis://localhost:6379',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    webhook: {
      service: 'Webhook Service',
      provider: 'fastapi',
      apiKey: '',
      endpoint: 'http://localhost:8000/webhooks',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    auth: {
      service: 'Authentication Service',
      provider: 'supabase',
      apiKey: '',
      endpoint: 'https://your-project.supabase.co',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'backend'
    },
    // Infrastructure Services
    monitoring: {
      service: 'Application Monitoring',
      provider: 'datadog',
      apiKey: '',
      endpoint: 'https://api.datadoghq.com',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    logging: {
      service: 'Centralized Logging',
      provider: 'elasticsearch',
      apiKey: '',
      endpoint: 'https://localhost:9200',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    storage: {
      service: 'Object Storage',
      provider: 's3',
      apiKey: '',
      endpoint: 'https://s3.amazonaws.com',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    },
    cdn: {
      service: 'Content Delivery Network',
      provider: 'cloudflare',
      apiKey: '',
      endpoint: 'https://api.cloudflare.com/client/v4',
      status: 'disconnected',
      isEnabled: true,
      showKey: false,
      category: 'infrastructure'
    }
  });

  const [testResults, setTestResults] = useState<{ [key: string]: string }>({});

  // Load available models and saved configurations from backend
  useEffect(() => {
    loadInitialData();
  }, [toast]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // Load available models from backend
      const modelsResponse = await backendService.getAvailableModels();
      if (modelsResponse.status === 'success') {
        setAvailableModels(modelsResponse.models);
        
        // Update configs with default models and endpoints
        setConfigs(prevConfigs => ({
          ...prevConfigs,
          stt: {
            ...prevConfigs.stt,
            model: modelsResponse.models.stt.default,
            endpoint: modelsResponse.models.stt.models.find((m: BackendModel) => m.value === modelsResponse.models.stt.default)?.endpoint || ''
          },
          llm: {
            ...prevConfigs.llm,
            model: modelsResponse.models.llm.default,
            endpoint: modelsResponse.models.llm.models.find((m: BackendModel) => m.value === modelsResponse.models.llm.default)?.endpoint || ''
          },
          tts: {
            ...prevConfigs.tts,
            model: modelsResponse.models.tts.default,
            endpoint: modelsResponse.models.tts.models.find((m: BackendModel) => m.value === modelsResponse.models.tts.default)?.endpoint || ''
          }
        }));
      }

      // Load saved settings from backend
      const settingsResponse = await backendService.loadModelSettings(); 
      if (settingsResponse.status === 'success' && settingsResponse.settings) {
        const settings = settingsResponse.settings;
        setConfigs(prevConfigs => ({
          ...prevConfigs,
          stt: {
            ...prevConfigs.stt,
            apiKey: settings.stt?.api_key || '',
            model: settings.stt?.model || prevConfigs.stt.model,
            endpoint: availableModels?.stt.models.find((m: BackendModel) => m.value === settings.stt?.model)?.endpoint || prevConfigs.stt.endpoint
          },
          llm: {
            ...prevConfigs.llm,
            apiKey: settings.llm?.api_key || '',
            model: settings.llm?.model || prevConfigs.llm.model,
            endpoint: availableModels?.llm.models.find((m: BackendModel) => m.value === settings.llm?.model)?.endpoint || prevConfigs.llm.endpoint
          },
          tts: {
            ...prevConfigs.tts,
            apiKey: settings.tts?.api_key || '',
            model: settings.tts?.model || prevConfigs.tts.model,
            endpoint: availableModels?.tts.models.find((m: BackendModel) => m.value === settings.tts?.model)?.endpoint || prevConfigs.tts.endpoint
          }
        }));

        toast({
          title: "Settings Loaded",
          description: "Saved model configurations have been loaded from backend.",
        });
      }
    } catch (error) {
      console.error('Error loading initial data:', error);
      toast({
        title: "Error",
        description: "Failed to load model configurations from backend.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Dynamic tech stack options based on available models from backend
  const techStackOptions = {
    stt: availableModels?.stt.models || [],
    tts: availableModels?.tts.models || [],
    llm: availableModels?.llm.models || [],
    emotion: [
      { value: 'dummy', label: 'Coming Soon (Dummy)', endpoint: 'N/A' }
    ],
    orchestration: [
      { value: 'dummy', label: 'Coming Soon (Dummy)', endpoint: 'N/A' }
    ]
  };

  // Backend services options (non-AI services)
  const backendStackOptions = {
    database: [
      { value: 'postgresql', label: 'PostgreSQL', endpoint: 'postgresql://localhost:5432/voiceengine' },
      { value: 'mysql', label: 'MySQL', endpoint: 'mysql://localhost:3306/voiceengine' },
      { value: 'mongodb', label: 'MongoDB', endpoint: 'mongodb://localhost:27017/voiceengine' },
      { value: 'supabase', label: 'Supabase', endpoint: 'https://your-project.supabase.co' }
    ],
    redis: [
      { value: 'redis', label: 'Redis', endpoint: 'redis://localhost:6379' },
      { value: 'redis-cloud', label: 'Redis Cloud', endpoint: 'redis://your-instance.redis.cloud:16379' },
      { value: 'elasticache', label: 'AWS ElastiCache', endpoint: 'redis://your-cluster.cache.amazonaws.com:6379' }
    ],
    webhook: [
      { value: 'fastapi', label: 'FastAPI Webhooks', endpoint: 'http://localhost:8000/webhooks' },
      { value: 'express', label: 'Express.js Webhooks', endpoint: 'http://localhost:3000/webhooks' },
      { value: 'flask', label: 'Flask Webhooks', endpoint: 'http://localhost:5000/webhooks' }
    ],
    auth: [
      { value: 'supabase', label: 'Supabase Auth', endpoint: 'https://your-project.supabase.co' },
      { value: 'firebase', label: 'Firebase Auth', endpoint: 'https://your-project.firebaseapp.com' },
      { value: 'auth0', label: 'Auth0', endpoint: 'https://your-domain.auth0.com' }
    ],
    // Infrastructure Services options
    monitoring: [
      { value: 'datadog', label: 'Datadog', endpoint: 'https://api.datadoghq.com' },
      { value: 'newrelic', label: 'New Relic', endpoint: 'https://api.newrelic.com' },
      { value: 'prometheus', label: 'Prometheus', endpoint: 'http://localhost:9090' }
    ],
    logging: [
      { value: 'elasticsearch', label: 'Elasticsearch', endpoint: 'https://localhost:9200' },
      { value: 'splunk', label: 'Splunk', endpoint: 'https://your-instance.splunkcloud.com' },
      { value: 'logstash', label: 'Logstash', endpoint: 'http://localhost:5044' }
    ],
    storage: [
      { value: 's3', label: 'AWS S3', endpoint: 'https://s3.amazonaws.com' },
      { value: 'gcs', label: 'Google Cloud Storage', endpoint: 'https://storage.googleapis.com' },
      { value: 'azure', label: 'Azure Blob Storage', endpoint: 'https://your-account.blob.core.windows.net' }
    ],
    cdn: [
      { value: 'cloudflare', label: 'Cloudflare', endpoint: 'https://api.cloudflare.com/client/v4' },
      { value: 'aws-cloudfront', label: 'AWS CloudFront', endpoint: 'https://cloudfront.amazonaws.com' },
      { value: 'fastly', label: 'Fastly', endpoint: 'https://api.fastly.com' }
    ]
  };

  const updateConfig = (service: string, field: string, value: any) => {
    setConfigs(prev => ({
      ...prev,
      [service]: {
        ...prev[service],
        [field]: value
      }
    }));
  };

  const toggleKeyVisibility = (service: string) => {
    updateConfig(service, 'showKey', !configs[service].showKey);
  };

  const testConnection = async (service: string) => {
    setTestResults(prev => ({ ...prev, [service]: 'testing' }));
    updateConfig(service, 'status', 'testing');

    try {
      // Simulate API test - in production, this would make actual API calls
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock test result based on whether API key is provided
      const success = configs[service].apiKey.length > 0 || configs[service].endpoint.length > 0;
      
      setTestResults(prev => ({ 
        ...prev, 
        [service]: success ? 'success' : 'error' 
      }));
      updateConfig(service, 'status', success ? 'connected' : 'error');
      
      toast({
        title: success ? "Connection Successful" : "Connection Failed",
        description: success 
          ? `${configs[service].service} is now connected and responding.`
          : `Failed to connect to ${configs[service].service}. Please check your configuration.`,
        variant: success ? "default" : "destructive"
      });
    } catch (error) {
      setTestResults(prev => ({ ...prev, [service]: 'error' }));
      updateConfig(service, 'status', 'error');
      toast({
        title: "Connection Error",
        description: `An error occurred while testing ${configs[service].service}.`,
        variant: "destructive"
      });
    }
  };

  const saveConfiguration = async () => {
    setIsLoading(true);
    try {
      // Save AI services configuration to backend
      const aiSettings = {
        stt: {
          model: configs.stt.model,
          api_key: configs.stt.apiKey
        },
        llm: {
          model: configs.llm.model,
          api_key: configs.llm.apiKey
        },
        tts: {
          model: configs.tts.model,
          api_key: configs.tts.apiKey
        }
      };

      const response = await backendService.saveModelSettings(aiSettings);
      
      if (response.status === 'success') {
        // Also save non-AI configurations to localStorage for now
        const nonAiConfigs = Object.keys(configs)
          .filter(key => !['stt', 'llm', 'tts', 'emotion', 'orchestration'].includes(key))
          .reduce((obj, key) => {
            obj[key] = configs[key];
            return obj;
          }, {} as typeof configs);
          
        localStorage.setItem('nonAiConfigurations', JSON.stringify(nonAiConfigs));
        
        toast({
          title: "Configuration Saved",
          description: "AI model configurations have been saved to backend successfully.",
        });
        console.log('AI configurations saved to backend:', aiSettings);
      } else {
        throw new Error('Failed to save to backend');
      }
    } catch (error) {
      console.error('Error saving configurations:', error);
      toast({
        title: "Save Failed",
        description: "Failed to save AI configurations to backend. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const exportConfiguration = () => {
    try {
      const configExport = {
        timestamp: new Date().toISOString(),
        configurations: configs
      };
      const dataStr = JSON.stringify(configExport, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `voice-engine-config-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
      
      toast({
        title: "Configuration Exported",
        description: "Configuration file has been downloaded.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export configuration.",
        variant: "destructive"
      });
    }
  };

  const getServiceIcon = (service: string) => {
    switch (service) {
      case 'stt': return <Mic className="w-5 h-5" />;
      case 'tts': return <Volume2 className="w-5 h-5" />;
      case 'llm': return <Brain className="w-5 h-5" />;
      case 'emotion': return <Heart className="w-5 h-5" />;
      case 'orchestration': return <Zap className="w-5 h-5" />;
      case 'database': return <Database className="w-5 h-5" />;
      case 'redis': return <Server className="w-5 h-5" />;
      case 'webhook': return <Zap className="w-5 h-5" />;
      case 'auth': return <Shield className="w-5 h-5" />;
      case 'monitoring': return <Settings className="w-5 h-5" />;
      case 'logging': return <Settings className="w-5 h-5" />;
      case 'storage': return <Cloud className="w-5 h-5" />;
      case 'cdn': return <Cloud className="w-5 h-5" />;
      default: return <Settings className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-400 border-green-400';
      case 'testing': return 'text-yellow-400 border-yellow-400';
      case 'error': return 'text-red-400 border-red-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  const getCategoryConfigs = (category: 'ai' | 'backend' | 'infrastructure') => {
    return Object.entries(configs).filter(([_, config]) => config.category === category);
  };

  const renderConfigurationCard = (service: string, config: APIConfig) => (
    <Card key={service} className="p-4 bg-gray-900/50 border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="text-blue-400">
            {getServiceIcon(service)}
          </div>
          <div>
            <h4 className="text-white font-medium">{config.service}</h4>
            <p className="text-gray-400 text-sm">Provider: {config.provider}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Switch 
            checked={config.isEnabled}
            onCheckedChange={(checked) => updateConfig(service, 'isEnabled', checked)}
          />
          <Badge variant="outline" className={getStatusColor(config.status)}>
            {config.status}
          </Badge>
        </div>
      </div>

      <div className="grid md:grid-cols-2 gap-4">
        {/* Show model selection for AI services only */}
        {['stt', 'llm', 'tts'].includes(service) ? (
          <div>
            <Label className="text-gray-300">Model</Label>
            <Select 
              value={config.model || ''}
              onValueChange={(value) => {
                const option = techStackOptions[service as keyof typeof techStackOptions]?.find(opt => opt.value === value);
                updateConfig(service, 'model', value);
                if (option) {
                  updateConfig(service, 'endpoint', option.endpoint);
                  
                }
              }}
              disabled={!config.isEnabled}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue placeholder="Select a model" />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {techStackOptions[service as keyof typeof techStackOptions]?.map((option) => (
                  <SelectItem key={option.value} value={option.value} className="text-white hover:bg-gray-700">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        ) : (
          <div>
            <Label className="text-gray-300">Provider</Label>
            <Select 
              value={config.provider}
              onValueChange={(value) => {
                const options = service in backendStackOptions ? 
                  backendStackOptions[service as keyof typeof backendStackOptions] : 
                  techStackOptions[service as keyof typeof techStackOptions];
                const option = options?.find(opt => opt.value === value);
                updateConfig(service, 'provider', value);
                if (option) {
                  updateConfig(service, 'endpoint', option.endpoint);
                }
              }}
              disabled={!config.isEnabled}
            >
              <SelectTrigger className="bg-gray-800 border-gray-700 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-gray-800 border-gray-700">
                {(service in backendStackOptions ? 
                  backendStackOptions[service as keyof typeof backendStackOptions] : 
                  techStackOptions[service as keyof typeof techStackOptions]
                )?.map((option) => (
                  <SelectItem key={option.value} value={option.value} className="text-white hover:bg-gray-700">
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        <div>
          <Label className="text-gray-300">API Endpoint</Label>
          <Input
            value={config.endpoint}
            onChange={(e) => updateConfig(service, 'endpoint', e.target.value)}
            placeholder={['emotion', 'orchestration'].includes(service) ? "Coming Soon" : "API Endpoint URL"}
            className="bg-gray-800 border-gray-700 text-white"
            disabled={['stt', 'llm', 'tts', 'emotion', 'orchestration'].includes(service)}
          />
          {['stt', 'llm', 'tts'].includes(service) && (
            <p className="text-xs text-gray-500 mt-1">Auto-configured based on selected model</p>
          )}
        </div>

        <div>
          <Label className="text-gray-300">API Key / Connection String</Label>
          <div className="flex space-x-2">
            <Input
              type={config.showKey ? 'text' : 'password'}
              value={config.apiKey}
              onChange={(e) => updateConfig(service, 'apiKey', e.target.value)}
              placeholder={['emotion', 'orchestration'].includes(service) ? "Not Required (Coming Soon)" : "Enter API Key or Connection String"}
              className="bg-gray-800 border-gray-700 text-white"
              disabled={['emotion', 'orchestration'].includes(service)}
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => toggleKeyVisibility(service)}
              className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
            >
              {config.showKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
          </div>
        </div>

        <div className="flex items-end">
          <Button
            onClick={() => testConnection(service)}
            variant="outline"
            size="sm"
            disabled={!config.isEnabled || testResults[service] === 'testing'}
            className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 w-full"
          >
            <TestTube className="w-4 h-4 mr-2" />
            {testResults[service] === 'testing' ? 'Testing...' : 'Test Connection'}
          </Button>
        </div>
      </div>

      {testResults[service] && (
        <div className={`mt-3 p-3 rounded border ${
          testResults[service] === 'success' 
            ? 'bg-green-500/10 border-green-500/30 text-green-300'
            : testResults[service] === 'error'
            ? 'bg-red-500/10 border-red-500/30 text-red-300'
            : 'bg-yellow-500/10 border-yellow-500/30 text-yellow-300'
        }`}>
          {testResults[service] === 'success' && (
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 mr-2" />
              Connection successful - Service responding correctly
            </div>
          )}
          {testResults[service] === 'error' && (
            <div className="flex items-center">
              <AlertCircle className="w-4 h-4 mr-2" />
              Connection failed - Check API key and endpoint
            </div>
          )}
          {testResults[service] === 'testing' && (
            <div className="flex items-center">
              <Zap className="w-4 h-4 mr-2 animate-pulse" />
              Testing connection...
            </div>
          )}
        </div>
      )}
    </Card>
  );

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Settings className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold text-white">AI Voice Engine API Configuration</h3>
            <Badge variant="outline" className="text-blue-400 border-blue-400">
              Enterprise Stack
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button 
              onClick={exportConfiguration}
              variant="outline"
              className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
            >
              <Key className="w-4 h-4 mr-2" />
              Export Config
            </Button>
            <Button 
              onClick={saveConfiguration}
              className="bg-blue-600 hover:bg-blue-700 text-white"
              disabled={isLoading}
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save All Configurations'}
            </Button>
          </div>
        </div>

        <Tabs defaultValue="ai" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900/50 border-gray-700">
            <TabsTrigger value="ai" className="text-white data-[state=active]:bg-blue-600">AI Services</TabsTrigger>
            <TabsTrigger value="backend" className="text-white data-[state=active]:bg-blue-600">Backend Services</TabsTrigger>
            <TabsTrigger value="infrastructure" className="text-white data-[state=active]:bg-blue-600">Infrastructure</TabsTrigger>
          </TabsList>

          <TabsContent value="ai" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('ai').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>

          <TabsContent value="backend" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('backend').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>

          <TabsContent value="infrastructure" className="space-y-4">
            <div className="grid gap-6">
              {getCategoryConfigs('infrastructure').map(([service, config]) => 
                renderConfigurationCard(service, config)
              )}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};
