# Live Transcription Integration

## Overview

This implementation adds real-time live transcription functionality to the Voice_agent_front_end11 frontend, connecting it to the voice calling agent backend in the `call_logging/dev-calling-agent` directory.

## What's Implemented

### 1. **TranscriptionService** (`src/services/api/TranscriptionService.ts`)
- Real-time API client for fetching live transcription data
- Polling mechanism with configurable intervals
- Subscription-based updates with multiple subscriber support
- Error handling and connection status monitoring
- Automatic cleanup and resource management

### 2. **useTranscription Hook** (`src/hooks/useTranscription.ts`)
- React hook for managing live transcription state
- Automatic connection handling and health monitoring
- Loading states and error management
- Manual refresh capability
- Auto-start/stop functionality

### 3. **LiveTranscriptionDisplay Component** (`src/components/voice/LiveTranscriptionDisplay.tsx`)
- Real-time transcription UI with connection status
- Expandable view (recent vs. full conversation)
- Automatic scroll with message formatting
- Connection status indicators
- Manual refresh and control buttons
- Responsive design with customizable height

### 4. **Enhanced BackendService** (`src/services/api/BackendService.ts`)
- Integration with the FastAPI backend
- Voice server control methods
- Health checking capabilities
- Transcription data fetching

## Backend API Integration

The frontend connects to these FastAPI endpoints:

- `GET /api/live-transcription` - Fetches the most recent live transcription
- `GET /api/transcriptions` - Gets recent transcription history
- `GET /api/health` - Backend health check
- `GET /api/server-status` - Voice server status
- `POST /api/start-server` - Start voice server
- `POST /api/stop-server` - Stop voice server

## How It Works

1. **Automatic Connection**: The `LiveTranscriptionDisplay` component automatically connects to the backend when mounted
2. **Real-time Polling**: Polls the `/api/live-transcription` endpoint every 2 seconds (configurable)
3. **Smart Updates**: Only updates the UI when new transcription data is available
4. **Connection Monitoring**: Continuously monitors backend connectivity and shows status
5. **Error Handling**: Gracefully handles connection failures and displays appropriate messages

## Usage in the Frontend

The live transcription is now integrated into the main voice interface page (`src/pages/Index.tsx`):

```tsx
<LiveTranscriptionDisplay 
  className="w-full"
  showControls={true}
  maxHeight="350px"
  pollingInterval={2000}
/>
```

## Configuration Options

### LiveTranscriptionDisplay Props:
- `className`: CSS classes for styling
- `showControls`: Show/hide control buttons (default: true)
- `maxHeight`: Maximum height of transcription area (default: "400px")
- `pollingInterval`: Polling frequency in milliseconds (default: 2000)

### useTranscription Options:
- `autoStart`: Automatically start polling (default: true)
- `pollingInterval`: Polling frequency (default: 2000ms)
- `baseUrl`: Backend API base URL (default: "http://localhost:8000")

## Features

### Visual Indicators
- 🟢 **Connected**: Green indicator when backend is available
- 🟡 **Connecting**: Yellow indicator when establishing connection
- 🔴 **Disconnected**: Red indicator when backend is unavailable

### Message Formatting
- **User Messages**: Blue background with microphone icon
- **AI Responses**: Green background with message icon
- **System Messages**: Gray background

### Controls
- **Refresh Button**: Manual refresh of transcription data
- **Expand/Collapse**: Toggle between recent and full conversation view
- **Real-time Status**: Shows connection status and last update time

## Backend Requirements

Ensure the FastAPI backend is running:

```bash
cd call_logging/dev-calling-agent
python app.py
```

The backend should be accessible at `http://localhost:8000` by default.

## Integration Points

### Main Voice Interface
The live transcription is positioned in the left column of the main interface, between the voice controller and AI behavior settings.

### Connection to Call Logging
The transcription data comes from the voice calling agent's call logs directory:
- Reads from `call_logs/call_transcript_*.txt`
- Reads from `call_logs/console_transcript_*.txt`
- Shows the most recent 10 lines for live display
- Full conversation available on expand

## Error Handling

The system gracefully handles:
- Backend unavailability
- Network connectivity issues
- API response errors
- Missing transcription files
- Malformed data

## Performance Considerations

- **Polling Optimization**: 2-second intervals balance real-time updates with server load
- **Data Caching**: Prevents unnecessary re-renders for unchanged data
- **Resource Cleanup**: Automatic cleanup of polling intervals and subscriptions
- **Error Recovery**: Automatic retry mechanisms for failed requests

## Customization

The transcription display can be customized by:
1. Adjusting polling intervals for different update frequencies
2. Modifying the UI styling and layout
3. Adding custom message formatting rules
4. Extending the API integration for additional features

## Future Enhancements

Potential improvements:
- WebSocket connection for true real-time updates
- Audio playback integration
- Advanced filtering and search capabilities
- Export functionality for transcriptions
- Real-time sentiment analysis display