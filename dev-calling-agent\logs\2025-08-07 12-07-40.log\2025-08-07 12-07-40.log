[ 2025-08-07 12:07:43,696 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 12:07:43,696 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 12:07:44,151 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4548 seconds.
[ 2025-08-07 12:07:48,251 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 12:07:56,678 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:07:56,678 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:07:56,679 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 12:07:57,568 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:07:57,569 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:07:57,569 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 12:07:57,569 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 12:07:57,578 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 12:07:57,623 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,624 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,625 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,625 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,627 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,627 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,627 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,628 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,629 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,630 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,632 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,636 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:07:57,826 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,827 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,828 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,828 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,828 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,828 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,829 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,829 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,829 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,829 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,829 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:57,830 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:07:58,374 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 12:08:12,070 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:08:12,281 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:08:12,283 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:08:13,257 ] 666 root - INFO - Call logging started for call ID: SCL_PQrpBFdz5nTi
[ 2025-08-07 12:08:14,451 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m0vb6f7zr52wkjcefqs2e, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:08:14,743 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m0vm3f80a5a4ynkmm0fhw, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:08:16,875 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m0xr9f82v6wq8a6evkbjj, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:08:17,922 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:08:17,926 ] 755 root - INFO - Call logging ended for call ID: SCL_PQrpBFdz5nTi
[ 2025-08-07 12:08:17,926 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:08:18,517 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m0zbjf85vk25j1v1q6wv5, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:08:18,980 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m0zt3f868wq5rvjw8bf3w, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:08:21,294 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m122df87sf0kgnx5av6zq, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:08:23,824 ] 877 livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m14gwfdwbfz2xea8j4mfz, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:08:31,221 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:08:54,157 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:09:16,606 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 12:09:16,608 ] 560 livekit.agents - INFO - shutting down worker
