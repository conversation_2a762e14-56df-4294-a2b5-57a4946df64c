
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { VoiceStatus } from '@/pages/Index';
import { useTranscription } from '@/hooks/useTranscription';
import { useAudioLevels } from '@/hooks/useAudioLevels';
import { Mic, Brain, MessageSquare, Volume2, Wifi, WifiOff, RefreshCw, Loader2 } from 'lucide-react';

interface RealTimeInteractionFeedbackProps {
  status: VoiceStatus;
}

export const RealTimeInteractionFeedback = ({ status }: RealTimeInteractionFeedbackProps) => {
  const [statusMessage, setStatusMessage] = useState('');

  // Use the optimized transcription hook with faster updates
  const {
    transcription: liveTranscription,
    isLoading: transcriptionLoading,
    isConnected: transcriptionConnected,
    error: transcriptionError,
    refresh: refreshTranscription
  } = useTranscription({
    autoStart: true,
    pollingInterval: 2000 // Faster updates for real-time feel
  });

  // Use real audio levels from backend with simulation fallback
  const {
    audioLevel: currentAudioLevel,
    hasActiveCalls,
    isConnected: audioConnected,
    error: audioError
  } = useAudioLevels({
    autoStart: true,
    pollingInterval: 200, // Fast updates for smooth visualization
    simulateFallback: true // Fall back to simulation when no backend
  });

  // Audio levels are now handled by useAudioLevels hook
  // No need for simulation here as it's handled by the service

  // Status messages
  useEffect(() => {
    switch (status) {
      case 'listening':
        setStatusMessage('Listening to your voice...');
        break;
      case 'processing':
        setStatusMessage('AI is thinking and analyzing...');
        break;
      case 'speaking':
        setStatusMessage('AI is responding...');
        break;
      default:
        setStatusMessage('Ready to start conversation');
    }
  }, [status]);

  return (
    <Card className="p-6 bg-white/10 backdrop-blur-sm border-white/20">
      <div className="space-y-6">
        {/* Status Bar */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {status === 'listening' && <Mic className="w-5 h-5 text-green-400 animate-pulse" />}
            {status === 'processing' && <Brain className="w-5 h-5 text-yellow-400 animate-pulse" />}
            {status === 'speaking' && <Volume2 className="w-5 h-5 text-blue-400 animate-pulse" />}
            <span className="text-white font-medium">{statusMessage}</span>
          </div>
          <Badge 
            className={`${
              status === 'listening' ? 'bg-green-600' :
              status === 'processing' ? 'bg-yellow-600' :
              status === 'speaking' ? 'bg-blue-600' : 'bg-gray-600'
            } text-white`}
          >
            {status.toUpperCase()}
          </Badge>
        </div>

        {/* Audio Input Levels - Updated Design */}
        <div className="space-y-4">
          <h4 className="text-white text-sm font-medium flex items-center">
            <Mic className="w-4 h-4 mr-2" />
            Audio Input Levels
          </h4>
          
          <div className="bg-gradient-to-br from-purple-900 to-purple-700 rounded-lg p-4">
            <div className="flex justify-between items-center mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-white text-sm font-medium">Level:</span>
                {hasActiveCalls && (
                  <span className="text-green-400 text-xs bg-green-400/20 px-2 py-1 rounded-full">
                    LIVE CALL
                  </span>
                )}
                {!audioConnected && (
                  <span className="text-yellow-400 text-xs bg-yellow-400/20 px-2 py-1 rounded-full">
                    SIMULATED
                  </span>
                )}
              </div>
              <span className="text-white text-lg font-bold">{Math.round(currentAudioLevel)}%</span>
            </div>
            
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="space-y-1">
                <div className={`text-sm font-medium ${
                  currentAudioLevel <= 60 ? 'text-green-400' : 'text-green-300'
                }`}>
                  Normal
                </div>
                <div className="text-xs text-gray-300">0-60%</div>
              </div>
              
              <div className="space-y-1">
                <div className={`text-sm font-medium ${
                  currentAudioLevel > 60 && currentAudioLevel <= 80 ? 'text-yellow-400' : 'text-yellow-300'
                }`}>
                  High
                </div>
                <div className="text-xs text-gray-300">60-80%</div>
              </div>
              
              <div className="space-y-1">
                <div className={`text-sm font-medium ${
                  currentAudioLevel > 80 ? 'text-red-400' : 'text-red-300'
                }`}>
                  Peak
                </div>
                <div className="text-xs text-gray-300">80-100%</div>
              </div>
            </div>
            
            {/* Enhanced Progress bar with voice intensity visualization */}
            <div className="mt-3 space-y-2">
              <Progress 
                value={currentAudioLevel} 
                className="h-3 bg-purple-800"
              />
              
              {/* Voice intensity bars */}
              <div className="flex items-end justify-center space-x-1 h-8">
                {Array.from({ length: 10 }, (_, i) => {
                  const barThreshold = (i + 1) * 10;
                  const isActive = currentAudioLevel >= barThreshold;
                  const barHeight = Math.min(100, Math.max(10, (currentAudioLevel / barThreshold) * 100));
                  
                  return (
                    <div
                      key={i}
                      className={`w-2 rounded-t transition-all duration-150 ${
                        isActive 
                          ? barThreshold <= 60 
                            ? 'bg-green-400' 
                            : barThreshold <= 80 
                            ? 'bg-yellow-400' 
                            : 'bg-red-400'
                          : 'bg-purple-600/50'
                      }`}
                      style={{ 
                        height: isActive ? `${barHeight}%` : '10%' 
                      }}
                    />
                  );
                })}
              </div>
            </div>
            
            {/* Status indicators */}
            <div className="mt-2 flex justify-between items-center text-xs">
              <span className="text-purple-200">
                {hasActiveCalls ? 'Live voice data' : audioConnected ? 'No active calls' : 'Simulated data'}
              </span>
              <div className="flex items-center space-x-1">
                {audioConnected ? (
                  <Wifi className="w-3 h-3 text-green-400" />
                ) : (
                  <WifiOff className="w-3 h-3 text-yellow-400" />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Live Transcription - Integrated with Backend */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <h4 className="text-white text-sm font-medium flex items-center">
              <MessageSquare className="w-4 h-4 mr-2" />
              Live Transcription
            </h4>
            
            <div className="flex items-center space-x-2">
              {transcriptionConnected ? (
                <Wifi className="w-4 h-4 text-green-400" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-400" />
              )}
              
              <Badge variant="outline" className={`text-xs ${
                transcriptionConnected ? 'text-green-400 border-green-400' : 'text-red-400 border-red-400'
              }`}>
                {transcriptionConnected ? 'Connected' : 'Disconnected'}
              </Badge>
              
              {liveTranscription && (
                <Badge variant="outline" className="text-xs text-gray-400 border-gray-400">
                  {liveTranscription.filename}
                </Badge>
              )}
              
              <button
                onClick={refreshTranscription}
                disabled={transcriptionLoading}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <RefreshCw className={`w-3 h-3 ${transcriptionLoading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>
          
          <div className="bg-black/20 rounded-lg p-3 min-h-[120px]">
            {transcriptionError ? (
              <div className="flex items-center justify-center h-full text-center">
                <div className="space-y-2">
                  <p className="text-red-400 text-sm font-medium">Connection Error</p>
                  <p className="text-gray-400 text-xs">{transcriptionError}</p>
                </div>
              </div>
            ) : !liveTranscription ? (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-400 text-sm">
                  {transcriptionConnected ? 'Waiting for voice conversation...' : 'Connecting to voice service...'}
                </p>
              </div>
            ) : (
              <ScrollArea className="h-24">
                <div className="space-y-2">
                  {liveTranscription.recent_content.split('\n').filter(line => line.trim()).map((line, index) => {
                    // Remove timestamps like [2025-08-02 12:26:34.786] from the line
                    const cleanLine = line
                      .replace(/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\]/g, '')
                      .replace(/\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\]/g, '')
                      .replace(/^\s*\d{2}:\d{2}:\d{2}\s*/, '') // Remove time at start
                      .trim();
                    
                    if (!cleanLine) return null;
                    
                    const isUserLine = cleanLine.toLowerCase().includes('user:') || cleanLine.toLowerCase().includes('you:');
                    const isAILine = cleanLine.toLowerCase().includes('ai:') || cleanLine.toLowerCase().includes('agent:');
                    
                    return (
                      <div key={index} className="text-sm">
                        <span className={
                          isUserLine ? 'text-blue-300' :
                          isAILine ? 'text-green-300' :
                          'text-white/90'
                        }>
                          {cleanLine}
                        </span>
                      </div>
                    );
                  }).filter(Boolean)}
                  {status === 'listening' && (
                    <span className="text-white/90 text-sm animate-pulse">|</span>
                  )}
                </div>
              </ScrollArea>
            )}
          </div>
          
          {liveTranscription && (
            <div className="text-xs text-gray-400 text-right">
              Last updated: {new Date(liveTranscription.last_modified).toLocaleTimeString()}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};
