import os, sys
from typing import Dict, Any, List
from concurrent.futures import Thread<PERSON>oolExecutor
from langchain_groq import <PERSON>tGroq
from langchain.schema import Document
from langgraph.graph import StateGraph, END
from dotenv import load_dotenv
load_dotenv()

from src.exception import CustomException
from src.logging.logger import logging
from src.utils.main_utils import measure_time
from src.agent.constant import GraphState
from src.agent.nodes.web_search import TaivilySearchTool
from src.agent.nodes.generation import GenerationTool
from src.agent.nodes.vectoredb_retrieval import RetrieverTool
from src.agent.nodes.grade import HallucinationGrader, RetrieverValidator
from src.agent.route import QuestionRouter


class AgenticRAG:
    def __init__(self) -> None:
        
        self.llm=self._load_model()
        
        # Initialize tools
        self.web_search_tool = TaivilySearchTool()
        self.rag_tool    = RetrieverTool(self.llm)
        self.generation_tool = GenerationTool(self.llm)
        self.document_validator = RetrieverValidator(self.llm)
        self.hallucination_grader = HallucinationGrader(self.llm)
        self.question_router = QuestionRouter(self.llm)
        
        # Build the graph
        self.workflow = self._build_graph()
    
    @measure_time
    def _load_model(self):
         with ThreadPoolExecutor(max_workers=4) as executor:
            # Start loading the LLM asynchronously
            llm_future = executor.submit(ChatGroq, model="llama3-8b-8192", api_key=os.getenv("GROQ_API_KEY"))
            # Wait for it to complete
            return  llm_future.result()

    def _build_graph(self):
        workflow = StateGraph(GraphState)
        
        # Add nodes
        workflow.add_node("route_question", self.question_router.route_question)
        workflow.add_node("rag_tool",self.rag_tool.search)
        workflow.add_node("web_search", self.web_search_tool.web_search)
        workflow.add_node("validate_documents", self.document_validator.validate_documents)
        workflow.add_node("generate", self.generation_tool.generate)
        workflow.add_node("grade_hallucination", self.hallucination_grader.grade_hallucination)
        
        # Define the flow
        workflow.set_entry_point("route_question")
        workflow.add_conditional_edges(
            "route_question",
            self._decide_next_step,
            {
                "rag_tool": "rag_tool",
                "web_search": "web_search"
            }
        )
   
        workflow.add_edge("rag_tool", "validate_documents")
        

        # Conditional edges
        # After validation: if valid → generate, else → web_search
        workflow.add_conditional_edges(
            "validate_documents",
            self._decide_after_validation,
            {
                "generate": "generate",
                "web_search": "web_search"
            }
        )
        # From web search → generate
        workflow.add_edge("web_search", "generate")

        # After generate → hallucination grader
        workflow.add_edge("generate", "grade_hallucination")

        # After hallucination grading: if hallucinated → web_search again, else → end
        workflow.add_conditional_edges(
            "grade_hallucination",
            self._decide_after_hallucination_check,
            {
                "web_search": "web_search",
                "end": END
            }
        )
        
        app = workflow.compile()
        # Save image after compiling
        app.get_graph().draw_mermaid_png(output_file_path="workflow_graph.png")  # Saves as workflow_graph.png

        
        return app

    def _decide_next_step(self, state: GraphState) -> str:
        if state.get("use_web_search", False):
            return "web_search"
        return "rag_tool"


    def _decide_after_validation(self, state: GraphState) -> str:
        if state.get("use_web_search", False):
            return "web_search"
        return "generate"

    def _decide_after_hallucination_check(self, state: GraphState) -> str:
        if state.get("use_web_search", False):
            return "web_search"
        return "end"

    @measure_time
    def run(self, question: str, documents: List[Document] = None) -> Dict[str, Any]:
        """Run the RAG agent workflow"""
        # question=question["user_transcript"]
        print(question)
        try:
            initial_state = {
                "question": question,
                "generation": "",
                "use_web_search": True,
                "documents": documents or []
            }
            
            result = self.workflow.invoke(initial_state)
            return result
        except Exception as e:
            logging.info(f"Agent Weorflow Error: {e}")
            CustomException(e,sys)

