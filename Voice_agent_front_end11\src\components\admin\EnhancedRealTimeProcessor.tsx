
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Activity, 
  Shield, 
  Database, 
  Zap, 
  Eye, 
  Lock,
  Filter,
  Download,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Key,
  Mic,
  Brain,
  Globe
} from 'lucide-react';

export const EnhancedRealTimeProcessor = () => {
  const [processingData, setProcessingData] = useState([]);
  const [selectedLayer, setSelectedLayer] = useState(1);

  useEffect(() => {
    // Simulate real-time data processing
    const interval = setInterval(() => {
      const newProcess = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        layer: Math.floor(Math.random() * 5) + 1,
        dataType: ['voice', 'text', 'metadata', 'emotion', 'intent'][Math.floor(Math.random() * 5)],
        status: 'processing',
        privacyStatus: 'tokenized',
        processingTime: Math.random() * 100 + 50,
        originalData: 'Hello, I need help with my account balance',
        tokenizedData: '[TOKEN_GREETING], [TOKEN_NEED] [TOKEN_HELP] with [TOKEN_ACCOUNT] [TOKEN_BALANCE]'
      };
      
      setProcessingData(prev => [newProcess, ...prev.slice(0, 9)]);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const dataLayers = [
    {
      layer: 1,
      name: 'Audio Capture & Encryption',
      description: 'Raw audio input with immediate AES-256 encryption',
      privacy: 'AES-256 encryption applied instantly',
      status: 'active',
      icon: <Mic className="w-4 h-4" />,
      details: {
        before: 'Raw Audio Waveform: [Binary Audio Data - 44.1kHz, 16-bit]',
        after: 'Encrypted Audio: AES-256[Binary_Data_Hash_12A4F...]',
        process: 'Audio stream encrypted in real-time before any processing'
      }
    },
    {
      layer: 2,
      name: 'Speech Processing & Tokenization',
      description: 'Whisper STT with personal data tokenization',
      privacy: 'Personal identifiers replaced with secure tokens',
      status: 'active',
      icon: <Zap className="w-4 h-4" />,
      details: {
        before: 'Text: "Hello, my name is John Smith, account number *********"',
        after: 'Tokenized: "Hello, my name is [TOKEN_NAME_A7B], account number [TOKEN_ACCT_X9Z]"',
        process: 'PII detection and secure tokenization using ML-based entity recognition'
      }
    },
    {
      layer: 3,
      name: 'Intent Recognition & Data Masking',
      description: 'Mistral AI analysis with privacy masking',
      privacy: 'Sensitive context masked while preserving intent',
      status: 'active',
      icon: <Brain className="w-4 h-4" />,
      details: {
        before: 'Intent Analysis on: "I want to transfer $5000 to account *********"',
        after: 'Masked Intent: "Transfer request: [TOKEN_AMOUNT] to [TOKEN_DEST_ACCT]"',
        process: 'Intent preserved while financial details are tokenized and secured'
      }
    },
    {
      layer: 4,
      name: 'Context Retention & Minimization',
      description: 'Context-aware response with data minimization',
      privacy: 'Only necessary context retained, rest purged',
      status: 'active',
      icon: <Database className="w-4 h-4" />,
      details: {
        before: 'Full Context: Customer history, preferences, transaction history',
        after: 'Minimal Context: Current session intent + basic auth tokens',
        process: 'GDPR-compliant data minimization - only session-critical data retained'
      }
    },
    {
      layer: 5,
      name: 'Secure Response & E2E Encryption',
      description: 'Coqui TTS with end-to-end encrypted delivery',
      privacy: 'Response encrypted before transmission',
      status: 'active',
      icon: <Globe className="w-4 h-4" />,
      details: {
        before: 'Response: "Your account balance is $15,247.83"',
        after: 'E2E Encrypted: TLS1.3[Response_Hash_B8C9D...] + Voice_Encrypted_Stream',
        process: 'Response tokenized, TTS generated, then fully encrypted for transmission'
      }
    }
  ];

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Activity className="w-6 h-6 text-green-400" />
            <h3 className="text-xl font-semibold text-white">Real-Time Data Processing Pipeline</h3>
            <Badge variant="outline" className="text-green-400 border-green-400">
              Live Processing
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        <Tabs defaultValue="layers" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900/50">
            <TabsTrigger value="layers" className="text-white">Processing Layers</TabsTrigger>
            <TabsTrigger value="comparison" className="text-white">Data Comparison</TabsTrigger>
            <TabsTrigger value="live-events" className="text-white">Live Events</TabsTrigger>
          </TabsList>

          <TabsContent value="layers" className="space-y-4">
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              {dataLayers.map((layer) => (
                <div 
                  key={layer.layer} 
                  className={`p-4 rounded-lg cursor-pointer transition-colors border ${
                    selectedLayer === layer.layer
                      ? 'bg-blue-600/20 border-blue-500'
                      : 'bg-gray-900/50 border-gray-700 hover:border-gray-600'
                  }`}
                  onClick={() => setSelectedLayer(layer.layer)}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="text-blue-400">{layer.icon}</div>
                      <span className="text-white font-medium">Layer {layer.layer}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <span className="text-xs text-green-400">Active</span>
                    </div>
                  </div>
                  
                  <h4 className="text-white text-sm font-medium mb-1">{layer.name}</h4>
                  <p className="text-gray-400 text-xs mb-3">{layer.description}</p>
                  
                  <div className="flex items-center space-x-1 p-2 bg-green-500/10 rounded border border-green-500/20">
                    <Lock className="w-3 h-3 text-green-400" />
                    <span className="text-xs text-green-300">{layer.privacy}</span>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="comparison" className="space-y-4">
            {selectedLayer && (
              <Card className="p-6 bg-gray-900/50 border-gray-700">
                <h4 className="text-lg font-semibold text-white mb-4">
                  Layer {selectedLayer} - Data Processing Details
                </h4>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h5 className="text-white font-medium mb-2 flex items-center">
                        <AlertCircle className="w-4 h-4 text-orange-400 mr-2" />
                        Before Processing (Sensitive)
                      </h5>
                      <div className="p-3 bg-red-500/10 border border-red-500/20 rounded">
                        <code className="text-red-300 text-sm">
                          {dataLayers[selectedLayer - 1]?.details.before}
                        </code>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h5 className="text-white font-medium mb-2 flex items-center">
                        <CheckCircle className="w-4 h-4 text-green-400 mr-2" />
                        After Processing (Protected)
                      </h5>
                      <div className="p-3 bg-green-500/10 border border-green-500/20 rounded">
                        <code className="text-green-300 text-sm">
                          {dataLayers[selectedLayer - 1]?.details.after}
                        </code>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <h5 className="text-white font-medium mb-2 flex items-center">
                    <Shield className="w-4 h-4 text-blue-400 mr-2" />
                    Privacy Protection Process
                  </h5>
                  <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded">
                    <p className="text-blue-300 text-sm">
                      {dataLayers[selectedLayer - 1]?.details.process}
                    </p>
                  </div>
                </div>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="live-events" className="space-y-4">
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {processingData.map((process) => (
                <div key={process.id} className="flex items-center justify-between p-3 bg-gray-900/30 rounded border border-gray-700">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                    <div>
                      <span className="text-white text-sm">Layer {process.layer} - {process.dataType}</span>
                      <div className="text-xs text-gray-400">{new Date(process.timestamp).toLocaleTimeString()}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-green-400 border-green-400 text-xs">
                      Privacy Protected
                    </Badge>
                    <span className="text-xs text-gray-400">{Math.round(process.processingTime)}ms</span>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};
