#!/usr/bin/env python3
import os
import sys
import asyncio
import threading
from typing import Dict, Any, Optional, List
from concurrent.futures import ThreadPoolExecutor

import time
import json
from datetime import datetime
from pathlib import Path
from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.logging.call_logger import call_logger
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm, stt
from livekit.plugins import silero, groq, cartesia
from livekit import rtc
from dotenv import load_dotenv

# FastAPI imports (kept for later use; server start commented out)
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from pydantic import BaseModel

load_dotenv()

# FastAPI Configuration models
class ModelConfig(BaseModel):
    model: str
    api_key: str

class SettingsConfig(BaseModel):
    stt: ModelConfig
    llm: ModelConfig
    tts: ModelConfig

class ServerStatus(BaseModel):
    status: str
    pid: Optional[int] = None
    uptime: Optional[float] = None

# ---------- Config helpers ----------
def load_model_config():
    """Load model configuration from saved settings"""
    config_file = Path("config/model_settings.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                logging.info(f"Loaded model configuration: {config}")
                return config
        except Exception as e:
            logging.error(f"Error loading model config: {e}")

    # Default configuration
    default_config = {
        "stt": {"model": "whisper-large-v3", "api_key": os.getenv("GROQ_API_KEY", "")},
        "llm": {"model": "llama3-70b-8192", "api_key": os.getenv("GROQ_API_KEY", "")},
        "tts": {"model": "sonic-2", "api_key": os.getenv("CARTESIA_API_KEY", "")}
    }
    logging.info(f"Using default model configuration: {default_config}")
    return default_config

def load_settings() -> Optional[Dict[str, Any]]:
    """Load saved model settings from file"""
    config_file_path = "config/model_settings.json"
    try:
        if os.path.exists(config_file_path):
            with open(config_file_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        logging.error(f"Error loading settings: {e}")
    return None

def save_settings(settings: Dict[str, Any]) -> bool:
    """Save model settings to file"""
    config_file_path = "config/model_settings.json"
    try:
        with open(config_file_path, 'w') as f:
            json.dump(settings, f, indent=2)
        return True
    except Exception as e:
        logging.error(f"Error saving settings: {e}")
        return False

# Load configuration at import time (safe)
MODEL_CONFIG = load_model_config()

# ---------- Globals ----------
session_language = None
session_language_config = None
current_call_id = None
transcription_logger = None

# IMPORTANT: do NOT initialize heavy components at module import time.
# Instead we define globals and initializer functions that main() will call.
rag_agent: Optional[AgenticRAG] = None
language_detector: Optional[LanguageDetector] = None
vector_tool_instance: Optional[VectorDatabaseTool] = None
web_tool_instance: Optional[WebSearchTool] = None

# ---------- Initialization helpers ----------
def initialize_rag():
    """Initialize RAG agent and language detector once (called from main only)."""
    global rag_agent, language_detector
    print("🚀 Initializing RAG agent and language detector at startup...")
    startup_time = time.time()
    try:
        rag_agent = AgenticRAG()
        language_detector = LanguageDetector()
        startup_duration = time.time() - startup_time
        print(f"✅ RAG agent and language detector initialized successfully in {startup_duration:.2f}s!")
    except Exception as e:
        print(f"⚠️ Failed to initialize RAG agent: {e}")
        rag_agent = None
        language_detector = None

def precreate_tools():
    """Pre-create vector and web tools using the preloaded RAG agent (call from main)."""
    global vector_tool_instance, web_tool_instance, rag_agent
    print("🔧 Pre-creating tools with preloaded RAG agent...")
    try:
        if rag_agent is not None:
            # VectorDatabaseTool expects an LLM from the rag_agent; adapt as needed
            vector_tool_instance = VectorDatabaseTool(rag_agent.llm)
            print("✅ Vector database tool created with preloaded RAG agent")
        else:
            print("⚠️ RAG agent not available, vector tool will be created on demand")
            vector_tool_instance = None

        web_tool_instance = WebSearchTool()
        print("✅ Web search tool created")
        print("✅ All tools pre-created successfully")
    except Exception as e:
        print(f"⚠️ Tool pre-creation failed: {e}")
        vector_tool_instance = None
        web_tool_instance = None

def get_rag_agent():
    """Return the preloaded rag_agent (initialize lazily if needed)."""
    global rag_agent
    if rag_agent is None:
        # Warn that lazy init is happening. Avoid this by pre-initializing in main.
        print("⚠️ RAG agent not preloaded, initializing now (this may cause delay)...")
        initialize_rag()
    return rag_agent

# ---------- Language detection helper ----------
def simple_language_detection(query: str):
    """Simple language detection (lazy-initialize detector)."""
    try:
        global language_detector
        if language_detector is None:
            language_detector = LanguageDetector()
        return language_detector.detect_language(query)
    except Exception as e:
        logging.error(f"Language detection error: {e}")
        return 'en', 'English', {'name': 'English'}

# ---------- Transcription logger ----------
class TranscriptionLogger:
    """Enhanced logger for user and agent transcriptions during calls"""

    def __init__(self, log_directory: str = "call_logs"):
        self.log_directory = log_directory
        os.makedirs(self.log_directory, exist_ok=True)
        self.call_transcript_file = None
        self.console_transcript_file = None
        self.current_file = None

    def start_call_transcription(self, call_id: str):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"call_transcript_{call_id}_{timestamp}.txt"
        self.call_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.call_transcript_file

        with open(self.call_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CALL TRANSCRIPTION LOG ===\n")
            f.write(f"Call ID: {call_id}\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started call transcription logging: {filename}")

    def start_console_transcription(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"console_transcript_{timestamp}.txt"
        self.console_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.console_transcript_file

        with open(self.console_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CONSOLE TRANSCRIPTION LOG ===\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started console transcription logging: {filename}")

    def log_transcription(self, text: str | list, is_final: bool = True, speaker: str = "USER"):
        if isinstance(text, list):
            text = " ".join(str(item) for item in text)
        elif not isinstance(text, str):
            text = str(text)

        if not text or not text.strip():
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        speaker_emoji = "👤" if speaker == "USER" else "🤖"
        log_entry = f"[{timestamp}] {speaker_emoji} {speaker}: {text.strip()}\n"
        target_file = self.current_file

        if target_file and is_final:
            try:
                with open(target_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
                    f.flush()
                print(f"📝 {speaker_emoji} {speaker}: {text.strip()}")
            except Exception as e:
                print(f"❌ Error writing transcription: {e}")

    def log_agent_response(self, text: str):
        self.log_transcription(text, is_final=True, speaker="AGENT")

    def log_user_speech(self, text: str):
        self.log_transcription(text, is_final=True, speaker="USER")

    def close_transcription(self):
        if self.current_file:
            try:
                with open(self.current_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n=== TRANSCRIPTION ENDED ===\n")
                    f.write(f"Ended: {datetime.now().isoformat()}\n")
                print(f"📝 Transcription session closed: {self.current_file}")
            except Exception as e:
                print(f"❌ Error closing transcription: {e}")

# ---------- Agent session enhancements ----------
class EnhancedCallLoggingSession(AgentSession):
    """Enhanced session with transcription logging and proper call lifecycle management"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.call_active = False

    async def start(self, agent, room):
        global current_call_id, transcription_logger

        @self.on("user_speech_committed")
        def on_user_speech(event):
            # event may contain participant info; log only if present
            if transcription_logger and hasattr(event, 'user_transcript') and event.user_transcript:
                transcription_logger.log_user_speech(event.user_transcript)
                if current_call_id:
                    call_logger.log_user_transcription(current_call_id, event.user_transcript)

        @self.on("agent_speech_committed")
        def on_agent_speech(event):
            if transcription_logger and hasattr(event, 'agent_transcript') and event.agent_transcript:
                transcription_logger.log_agent_response(event.agent_transcript)
                if current_call_id:
                    call_logger.log_agent_response(current_call_id, event.agent_transcript)

        @self.on("conversation_item_added")
        def on_conversation_item(event):
            if transcription_logger and hasattr(event, 'item'):
                item = event.item
                if hasattr(item, 'content') and item.content:
                    speaker = "USER" if hasattr(item, 'role') and item.role == 'user' else "AGENT"
                    transcription_logger.log_transcription(item.content, is_final=True, speaker=speaker)

        self.call_active = True
        await super().start(agent=agent, room=room)

    async def generate_reply(self, **kwargs):
        global current_call_id, transcription_logger
        reply = await super().generate_reply(**kwargs)

        if reply:
            response_text = None
            if hasattr(reply, 'content') and reply.content:
                response_text = reply.content
            elif hasattr(reply, 'text') and reply.text:
                response_text = reply.text
            elif isinstance(reply, str):
                response_text = reply

            if response_text and transcription_logger:
                transcription_logger.log_agent_response(response_text)
            if response_text and current_call_id:
                call_logger.log_agent_response(current_call_id, response_text)

        return reply

    async def aclose(self):
        global transcription_logger
        try:
            await super().aclose()
        finally:
            self.call_active = False
            if transcription_logger:
                transcription_logger.close_transcription()

# ---------- Tools as llm.function_tool wrappers ----------
FAST_RESPONSES = {
    'hi': {
        'no_results': "Mere paas is technical sawal ka jawaab nahi hai.",
        'error': "Knowledge base mein kuch technical problem hai."
    },
    'ta': {
        'no_results': "Enakku indha technical kelvikku information illa.",
        'error': "Knowledge base la konjam technical problem irukku."
    },
    'te': {
        'no_results': "Naa daggara ee technical prashnaku information ledu.",
        'error': "Knowledge base lo konni technical samasyalu unnaayi."
    },
    'de': {
        'no_results': "Ich habe keine technischen Informationen dazu.",
        'error': "Es gibt ein technisches Problem mit der Wissensdatenbank."
    },
    'fr': {
        'no_results': "Je n'ai pas d'informations techniques à ce sujet.",
        'error': "Il y a un problème technique avec la base de connaissances."
    },
    'en': {
        'no_results': "I don't have technical information about this.",
        'error': "Technical issue with knowledge base."
    }
}

def create_vector_database_tool():
    @llm.function_tool(
        name="vector_database_search",
        description="Search technical documentation. Use ONLY for: ABB switchgear, transformers, RAG, NLP, technical docs. Returns complete answer - no additional tools needed."
    )
    async def vector_database_search(query: str) -> str:
        start_time = time.time()
        try:
            global session_language, session_language_config, vector_tool_instance

            if vector_tool_instance is None:
                print("⚠️ Vector tool not preloaded, creating on demand...")
                rag = get_rag_agent()
                vector_tool_instance = VectorDatabaseTool(rag.llm)

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"🌍 Language detected: {lang_name} ({lang_code})")

            result = vector_tool_instance.search_documents(
                query,
                session_language or 'en'
            )

            lang = session_language or 'en'
            if result.get('is_relevant'):
                response = f"FINAL_ANSWER: {result.get('results')}"
                logging.info(f"✅ Vector search completed: {time.time() - start_time:.2f}s")
            else:
                response = f"FINAL_ANSWER: {FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['no_results']}"
                logging.info(f"❌ No results found: {time.time() - start_time:.2f}s")

            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = response.replace("FINAL_ANSWER:", f"FINAL_ANSWER [{lang_name}]:")

            return response

        except Exception as e:
            logging.error(f"Vector tool error: {e}")
            lang = session_language or 'en'
            return f"FINAL_ANSWER: {FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['error']}"

    return vector_database_search

def create_web_search_tool():
    @llm.function_tool(
        name="web_search",
        description="Search current information. Use for: Chief Ministers, Prime Ministers, current events, news, weather. Returns complete answer - no additional tools needed."
    )
    async def web_search(query: str) -> str:
        start_time = time.time()
        try:
            global session_language, session_language_config, web_tool_instance

            if web_tool_instance is None:
                print("⚠️ Web tool not preloaded, creating on demand...")
                web_tool_instance = WebSearchTool()

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"🌍 Language detected: {lang_name} ({lang_code})")

            enhanced_query = f"{query} as of 2025"
            logging.info(f"Enhanced web search query: {enhanced_query}")

            result = web_tool_instance.search_web(
                enhanced_query,
                session_language or 'en'
            )

            response = f"FINAL_ANSWER: {result.get('results')}"
            logging.info(f"🌐 Web search completed: {time.time() - start_time:.2f}s, {result.get('result_count')} results")

            lang = session_language or 'en'
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = response.replace("FINAL_ANSWER:", f"FINAL_ANSWER [{lang_name}]:")

            return response

        except Exception as e:
            logging.error(f"Web search error: {e}")
            lang = session_language or 'en'
            web_errors = {
                'hi': "Web search mein technical problem hai.",
                'ta': "Web search la technical problem irukku.",
                'te': "Web search lo technical problem undi.",
                'de': "Technisches Problem bei der Websuche.",
                'fr': "Problème technique avec la recherche web.",
                'en': "Technical issue with web search."
            }
            return f"FINAL_ANSWER: {web_errors.get(lang, web_errors['en'])}"

    return web_search

# ---------- UltraFastLanguageAgent ----------
def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

🚨 CRITICAL RESPONSE RULES 🚨

1. SINGLE TOOL CALL ONLY:
   - Use ONLY ONE tool per user question
   - NEVER call multiple tools for the same query
   - When tool returns "FINAL_ANSWER:", extract and speak only the answer part
   - Do NOT mention tool names when speaking

... (shortened for brevity in this listing; keep your full instructions here) ...
"""
    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful voice AI assistant responding in {lang_name}.
... (keep your multilingual template here) ...
"""

class UltraFastLanguageAgent(Agent):
    def __init__(self, llm, tools):
        initial_instructions = """⚡ MULTILINGUAL AI ASSISTANT ⚡
... (keep your full initial instructions here) ...
"""
        super().__init__(llm=llm, tools=tools, instructions=initial_instructions)
        self.current_language = None
        self.language_config = None
        self.language_locked = False

    async def _handle_user_message(self, message):
        start_time = time.time()
        global session_language, session_language_config, current_call_id, transcription_logger

        if message.content:
            if transcription_logger:
                transcription_logger.log_user_speech(message.content)
            if current_call_id:
                call_logger.log_user_transcription(current_call_id, message.content)

            if session_language is None:
                lang_code, lang_name, lang_config = simple_language_detection(message.content)
                if lang_code != 'unknown':
                    session_language = lang_code
                    session_language_config = lang_config
                    self.current_language = lang_code
                    self.language_config = lang_config
                    self.language_locked = True
                    logging.info(f"⚡ LANGUAGE LOCKED: {lang_name} ({lang_code}) in {time.time() - start_time:.3f}s")
                    if current_call_id:
                        call_logger.set_language(current_call_id, f"{lang_name} ({lang_code})")
                    self.instructions = get_language_specific_instructions(lang_code, lang_config)
            else:
                lang_code = session_language
                lang_name = session_language_config.get('name', 'Unknown') if session_language_config else 'Unknown'
                logging.info(f"🔒 USING LOCKED LANGUAGE: {lang_name}")

        response = await super()._handle_user_message(message)

        if response and hasattr(response, 'content'):
            content = response.content
            if "FINAL_ANSWER:" in content:
                answer_part = content.split("FINAL_ANSWER:", 1)[1].strip()
                if answer_part.startswith("[") and "]" in answer_part:
                    answer_part = answer_part.split("]", 1)[1].strip()
                response.content = answer_part
                if transcription_logger:
                    transcription_logger.log_agent_response(answer_part)
                if current_call_id:
                    call_logger.log_agent_response(current_call_id, answer_part)

        return response

# ---------- Entrypoint ----------
async def entrypoint(ctx: JobContext):
    """Enhanced entrypoint with proper call lifecycle and transcription logging."""
    global session_language, session_language_config, current_call_id, transcription_logger
    try:
        print("🚀 Starting enhanced voice agent with transcription logging...")
        start_time = time.time()

        await ctx.connect()

        session_language = None
        session_language_config = None
        current_call_id = None

        transcription_logger = TranscriptionLogger()

        print("⏳ Waiting for participant...")
        participant = await ctx.wait_for_participant()

        is_sip_call = hasattr(participant, 'kind') and participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP

        if is_sip_call:
            current_call_id = call_logger.start_call(participant)
            if current_call_id:
                print(f"📞 SIP Call detected - Call ID: {current_call_id}")
                print(f"📱 Caller: {participant.attributes.get('sip.phoneNumber', 'Unknown')}")
                transcription_logger.start_call_transcription(current_call_id)
                logging.info(f"Call logging started for call ID: {current_call_id}")
            else:
                print("⚠️ Failed to start call logging")
        else:
            print("🖥️ Console/Web participant detected - Starting console transcription")
            transcription_logger.start_console_transcription()

        if rag_agent is not None:
            print("✅ Using preloaded RAG agent - no initialization delay!")
        else:
            print("⚠️ RAG agent not preloaded - may experience delays during first use")

        if vector_tool_instance is not None:
            print("✅ Using preloaded vector database tool")
        else:
            print("⚠️ Vector tool not preloaded")

        if web_tool_instance is not None:
            print("✅ Using preloaded web search tool")
        else:
            print("⚠️ Web tool not preloaded")

        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        stt_model = MODEL_CONFIG.get("stt", {}).get("model", "whisper-large-v3")
        llm_model = MODEL_CONFIG.get("llm", {}).get("model", "llama3-70b-8192")
        tts_model = MODEL_CONFIG.get("tts", {}).get("model", "sonic-2")

        stt_api_key = MODEL_CONFIG.get("stt", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        llm_api_key = MODEL_CONFIG.get("llm", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        tts_api_key = MODEL_CONFIG.get("tts", {}).get("api_key") or os.getenv("CARTESIA_API_KEY")

        print(f"🎤 Using STT model: {stt_model}")
        print(f"🧠 Using LLM model: {llm_model}")
        print(f"🔊 Using TTS model: {tts_model}")

        session = EnhancedCallLoggingSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model=stt_model,
                detect_language=True,
                api_key=stt_api_key
            ),
            llm=groq.LLM(
                model=llm_model,
                temperature=0.05,
                api_key=llm_api_key
            ),
            tts=cartesia.TTS(
                model=tts_model,
                api_key=tts_api_key
            ),
        )

        agent = UltraFastLanguageAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Agent ready in {time.time() - start_time:.2f}s")

        await session.generate_reply(
            instructions="Say: 'Hello! How can I help?' Keep it under 6 words. Do not mention tools."
        )

        print("🎙️ Session active - waiting for conversation...")
        try:
            while session.call_active:
                await asyncio.sleep(1)
        except Exception as e:
            logging.error(f"Session monitoring error: {e}")
        finally:
            await session.aclose()

    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)
    finally:
        if transcription_logger:
            transcription_logger.close_transcription()
        if current_call_id:
            call_logger.end_call(current_call_id)
            print(f"📞 Call logging ended for call ID: {current_call_id}")
            logging.info(f"Call logging ended for call ID: {current_call_id}")

# ---------- Main launcher ----------
if __name__ == "__main__":
    # IMPORTANT: do the heavy initialization here so it's executed only once in the main process
    from livekit.agents import cli, WorkerOptions

    # create logs directory
    os.makedirs("call_logs", exist_ok=True)

    # initialize rag and tools BEFORE starting LiveKit workers
    initialize_rag()
    precreate_tools()

    print("🎙️ Starting LiveKit Agent... (main process initialized RAG & tools)")

    # Launch CLI + workers with our entrypoint. Worker processes will import this module
    # but won't re-run the main block above.
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="telephony_agent"))
