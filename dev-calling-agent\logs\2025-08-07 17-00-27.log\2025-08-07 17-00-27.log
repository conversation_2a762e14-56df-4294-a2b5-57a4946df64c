[ 2025-08-07 17:00:31,470 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'HwGQf9BXnooaNMX680n7zPW_bxVL7Tw7bBu_3gRxiho='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 17:00:31,470 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 17:00:31,880 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4101 seconds.
[ 2025-08-07 17:00:36,055 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 17:00:44,909 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 17:00:44,909 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 17:00:44,911 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 17:00:45,749 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 17:00:45,749 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 17:00:45,749 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 17:00:45,750 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 17:00:45,757 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 17:00:45,830 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,830 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,830 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,832 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,832 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,833 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,833 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,833 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,834 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,834 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,834 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:45,835 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:00:46,044 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,045 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,045 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,045 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,046 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,047 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:00:46,543 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 17:02:44,116 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:47,122 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:47,305 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:50,292 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:51,597 ] 836 livekit.agents - WARNING - assignment for job AJ_VUx8HmroUHsF timed out
[ 2025-08-07 17:02:51,600 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:02:52,122 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:54,640 ] 836 livekit.agents - WARNING - assignment for job AJ_VUx8HmroUHsF timed out
[ 2025-08-07 17:02:54,641 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:02:54,811 ] 836 livekit.agents - WARNING - assignment for job AJ_fAsXExdiUU2M timed out
[ 2025-08-07 17:02:54,812 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:02:55,406 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:02:57,798 ] 836 livekit.agents - WARNING - assignment for job AJ_fAsXExdiUU2M timed out
[ 2025-08-07 17:02:57,800 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:02:59,598 ] 836 livekit.agents - WARNING - assignment for job AJ_VUx8HmroUHsF timed out
[ 2025-08-07 17:02:59,598 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:03:02,910 ] 836 livekit.agents - WARNING - assignment for job AJ_fAsXExdiUU2M timed out
[ 2025-08-07 17:03:02,912 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:03:12,845 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:03:15,849 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:03:20,351 ] 836 livekit.agents - WARNING - assignment for job AJ_fAsXExdiUU2M timed out
[ 2025-08-07 17:03:20,353 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:03:20,846 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:03:23,374 ] 836 livekit.agents - WARNING - assignment for job AJ_fAsXExdiUU2M timed out
[ 2025-08-07 17:03:23,376 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:03:23,910 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:03:23,911 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:03:33,857 ] 252 livekit.agents - WARNING - The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. 
[ 2025-08-07 17:04:11,658 ] 167 livekit - WARNING - livekit::rtc_engine:395:livekit::rtc_engine - failed to connect: Connection("wait_pc_connection timed out"), retrying... (1/3)
[ 2025-08-07 17:07:01,162 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:07:08,673 ] 836 livekit.agents - WARNING - assignment for job AJ_w26eGfYe8kRy timed out
[ 2025-08-07 17:07:08,675 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 17:07:33,098 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:07:36,167 ] 666 root - INFO - Call logging started for call ID: SCL_hX5E9whNkSfS
[ 2025-08-07 17:07:43,739 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 17:07:43,742 ] 755 root - INFO - Call logging ended for call ID: SCL_hX5E9whNkSfS
[ 2025-08-07 17:07:43,742 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 17:07:47,753 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 17:07:47,755 ] 207 root - INFO - \u26a1 Language detection: 0.673s
[ 2025-08-07 17:07:47,757 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 17:07:47,757 ] 513 root - INFO - Enhanced web search query: card picking as of 2025 as of 2025
[ 2025-08-07 17:07:47,759 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:07:47,760 ] 45 root - INFO - \U0001f310 Fast web search: 'card picking as of 2025 as of ...'
[ 2025-08-07 17:07:50,840 ] 59 root - INFO - \u26a1 Web search: 3.08s, 2 results
[ 2025-08-07 17:07:50,841 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.0823 seconds.
[ 2025-08-07 17:07:50,845 ] 525 root - INFO - \U0001f310 Web search: 3.76s, 2 results
[ 2025-08-07 17:07:51,468 ] 513 root - INFO - Enhanced web search query: pick a card reading August 2025 as of 2025 as of 2025
[ 2025-08-07 17:07:51,472 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:07:51,473 ] 45 root - INFO - \U0001f310 Fast web search: 'pick a card reading August 202...'
[ 2025-08-07 17:07:51,955 ] 513 root - INFO - Enhanced web search query: current weather as of 2025 as of 2025
[ 2025-08-07 17:07:51,959 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:07:51,964 ] 45 root - INFO - \U0001f310 Fast web search: 'current weather as of 2025 as ...'
[ 2025-08-07 17:07:53,800 ] 59 root - INFO - \u26a1 Web search: 2.33s, 2 results
[ 2025-08-07 17:07:53,803 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3311 seconds.
[ 2025-08-07 17:07:53,805 ] 525 root - INFO - \U0001f310 Web search: 2.34s, 2 results
[ 2025-08-07 17:07:56,082 ] 59 root - INFO - \u26a1 Web search: 4.12s, 2 results
[ 2025-08-07 17:07:56,089 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.1293 seconds.
[ 2025-08-07 17:07:56,101 ] 525 root - INFO - \U0001f310 Web search: 4.15s, 2 results
[ 2025-08-07 17:07:57,702 ] 513 root - INFO - Enhanced web search query: ABB switchgear as of 2025 as of 2025
[ 2025-08-07 17:07:57,705 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:07:57,708 ] 45 root - INFO - \U0001f310 Fast web search: 'ABB switchgear as of 2025 as o...'
[ 2025-08-07 17:07:59,809 ] 59 root - INFO - \u26a1 Web search: 2.10s, 2 results
[ 2025-08-07 17:07:59,812 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.1064 seconds.
[ 2025-08-07 17:07:59,813 ] 525 root - INFO - \U0001f310 Web search: 2.11s, 2 results
[ 2025-08-07 17:08:00,382 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:00,386 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear...'
[ 2025-08-07 17:08:02,342 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-08-07 17:08:04,322 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:08:04,324 ] 159 root - INFO - \u26a1 New response generated in 3.93s
[ 2025-08-07 17:08:04,325 ] 51 root - INFO - Vector DB search completed in 3.94s
[ 2025-08-07 17:08:04,326 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:04,327 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.9444 seconds.
[ 2025-08-07 17:08:04,329 ] 457 root - INFO - \u2705 Vector search: 3.95s
[ 2025-08-07 17:08:09,749 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:09,756 ] 37 root - INFO - \U0001f50d Vector DB search for: '\u0c2a\u0c4d\u0c30\u0c3e\u0c2c\u0c4d\u0c32\u0c41\u0c2e\u0c4d \u0c39\u0c3e\u0c15\u0c3e\u0c1f\u0c41 \u0c28\u0c46\u0c1f\u0c4d\u0c35\u0c30\u0c4d\u0c15\u0c4d \u0c32\u0c47\u0c38\u0c41 \u0c2a\u0c4d\u0c30\u0c3f\u0c2f\u0c24\u0c2e\u0c15\u0c4d\u0c15\u0c3f...'
[ 2025-08-07 17:08:11,306 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:08:11,307 ] 159 root - INFO - \u26a1 New response generated in 1.55s
[ 2025-08-07 17:08:11,308 ] 51 root - INFO - Vector DB search completed in 1.55s
[ 2025-08-07 17:08:11,309 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:11,310 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.5618 seconds.
[ 2025-08-07 17:08:11,311 ] 457 root - INFO - \u2705 Vector search: 1.56s
[ 2025-08-07 17:08:13,076 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:13,077 ] 37 root - INFO - \U0001f50d Vector DB search for: 'network slogan day no landlord I'm just a broker...'
[ 2025-08-07 17:08:14,244 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:08:14,245 ] 159 root - INFO - \u26a1 New response generated in 1.17s
[ 2025-08-07 17:08:14,245 ] 51 root - INFO - Vector DB search completed in 1.17s
[ 2025-08-07 17:08:14,246 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:14,247 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.1708 seconds.
[ 2025-08-07 17:08:14,248 ] 457 root - INFO - \u2705 Vector search: 1.17s
[ 2025-08-07 17:08:18,780 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:18,782 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:19,861 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:08:19,863 ] 159 root - INFO - \u26a1 New response generated in 1.08s
[ 2025-08-07 17:08:19,863 ] 51 root - INFO - Vector DB search completed in 1.08s
[ 2025-08-07 17:08:19,864 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:19,864 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0840 seconds.
[ 2025-08-07 17:08:19,867 ] 457 root - INFO - \u2705 Vector search: 1.09s
[ 2025-08-07 17:08:20,420 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:20,422 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:20,423 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.001s
[ 2025-08-07 17:08:20,423 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 17:08:20,424 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:20,424 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0041 seconds.
[ 2025-08-07 17:08:20,427 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:21,333 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:21,334 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:21,336 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:08:21,336 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 17:08:21,336 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:21,337 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0047 seconds.
[ 2025-08-07 17:08:21,339 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:21,960 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:21,963 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:21,963 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:08:21,964 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 17:08:21,964 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:21,966 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0059 seconds.
[ 2025-08-07 17:08:21,967 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:21,971 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-07 17:08:34,537 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:34,541 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:34,543 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:08:34,543 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 17:08:34,546 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:34,547 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0100 seconds.
[ 2025-08-07 17:08:34,549 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:34,921 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:34,922 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:34,922 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:08:34,923 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 17:08:34,924 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:34,924 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0040 seconds.
[ 2025-08-07 17:08:34,927 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:35,697 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:35,699 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages...'
[ 2025-08-07 17:08:35,702 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:08:35,706 ] 51 root - INFO - Vector DB search completed in 0.01s
[ 2025-08-07 17:08:35,707 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:35,708 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0106 seconds.
[ 2025-08-07 17:08:35,710 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 17:08:40,754 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:08:40,755 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVP joint operational voltages 146 12 3d...'
[ 2025-08-07 17:08:41,995 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:08:41,997 ] 159 root - INFO - \u26a1 New response generated in 1.24s
[ 2025-08-07 17:08:41,998 ] 51 root - INFO - Vector DB search completed in 1.24s
[ 2025-08-07 17:08:42,000 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:08:42,003 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.2487 seconds.
[ 2025-08-07 17:08:42,023 ] 457 root - INFO - \u2705 Vector search: 1.27s
[ 2025-08-07 17:09:02,409 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 17:09:26,919 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 17:09:27,343 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:09:27,346 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:10:26,388 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:10:27,697 ] 666 root - INFO - Call logging started for call ID: SCL_GPSLrKEtLZaT
[ 2025-08-07 17:10:33,409 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 17:10:33,423 ] 755 root - INFO - Call logging ended for call ID: SCL_GPSLrKEtLZaT
[ 2025-08-07 17:10:33,426 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 17:10:41,308 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 17:10:41,310 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 17:10:41,311 ] 513 root - INFO - Enhanced web search query: Germany presence as of 2025 as of 2025
[ 2025-08-07 17:10:41,313 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:10:41,315 ] 45 root - INFO - \U0001f310 Fast web search: 'Germany presence as of 2025 as...'
[ 2025-08-07 17:10:43,973 ] 59 root - INFO - \u26a1 Web search: 2.66s, 2 results
[ 2025-08-07 17:10:43,973 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6601 seconds.
[ 2025-08-07 17:10:43,974 ] 525 root - INFO - \U0001f310 Web search: 2.67s, 2 results
[ 2025-08-07 17:10:44,236 ] 513 root - INFO - Enhanced web search query: Germany current news as of 2025 as of 2025
[ 2025-08-07 17:10:44,239 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:10:44,239 ] 45 root - INFO - \U0001f310 Fast web search: 'Germany current news as of 202...'
[ 2025-08-07 17:10:46,591 ] 59 root - INFO - \u26a1 Web search: 2.35s, 2 results
[ 2025-08-07 17:10:46,592 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3535 seconds.
[ 2025-08-07 17:10:46,594 ] 525 root - INFO - \U0001f310 Web search: 2.36s, 2 results
[ 2025-08-07 17:10:47,255 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:47,259 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-07 17:10:50,383 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:50,384 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear installation manual...'
[ 2025-08-07 17:10:51,622 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:10:51,624 ] 159 root - INFO - \u26a1 New response generated in 4.35s
[ 2025-08-07 17:10:51,624 ] 51 root - INFO - Vector DB search completed in 4.37s
[ 2025-08-07 17:10:51,624 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:10:51,626 ] 68 root - INFO - \u2705 Finished 'search_documents' in 4.3712 seconds.
[ 2025-08-07 17:10:51,628 ] 457 root - INFO - \u2705 Vector search: 4.37s
[ 2025-08-07 17:10:52,007 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:52,007 ] 37 root - INFO - \U0001f50d Vector DB search for: 'transformer technical documentation...'
[ 2025-08-07 17:10:52,596 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:10:52,598 ] 159 root - INFO - \u26a1 New response generated in 2.21s
[ 2025-08-07 17:10:52,602 ] 51 root - INFO - Vector DB search completed in 2.22s
[ 2025-08-07 17:10:52,608 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:10:52,610 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.2279 seconds.
[ 2025-08-07 17:10:52,613 ] 457 root - INFO - \u2705 Vector search: 2.23s
[ 2025-08-07 17:10:53,013 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:53,013 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB transformer maintenance procedures...'
[ 2025-08-07 17:10:54,465 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:10:54,470 ] 159 root - INFO - \u26a1 New response generated in 2.46s
[ 2025-08-07 17:10:54,476 ] 51 root - INFO - Vector DB search completed in 2.47s
[ 2025-08-07 17:10:54,478 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:10:54,494 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.4875 seconds.
[ 2025-08-07 17:10:54,527 ] 457 root - INFO - \u2705 Vector search: 2.52s
[ 2025-08-07 17:10:54,532 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-07 17:10:54,713 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:10:54,713 ] 159 root - INFO - \u26a1 New response generated in 1.70s
[ 2025-08-07 17:10:54,713 ] 51 root - INFO - Vector DB search completed in 1.70s
[ 2025-08-07 17:10:54,714 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:10:54,714 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.7017 seconds.
[ 2025-08-07 17:10:54,714 ] 457 root - INFO - \u2705 Vector search: 1.70s
[ 2025-08-07 17:10:55,284 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:55,289 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG NLP technical documentation...'
[ 2025-08-07 17:10:57,667 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:10:57,668 ] 159 root - INFO - \u26a1 New response generated in 2.38s
[ 2025-08-07 17:10:57,669 ] 51 root - INFO - Vector DB search completed in 2.38s
[ 2025-08-07 17:10:57,671 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:10:57,673 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.3876 seconds.
[ 2025-08-07 17:10:57,674 ] 457 root - INFO - \u2705 Vector search: 2.39s
[ 2025-08-07 17:10:58,664 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:10:58,666 ] 37 root - INFO - \U0001f50d Vector DB search for: 'company policies for remote work...'
[ 2025-08-07 17:11:00,071 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:11:00,072 ] 159 root - INFO - \u26a1 New response generated in 1.40s
[ 2025-08-07 17:11:00,074 ] 51 root - INFO - Vector DB search completed in 1.41s
[ 2025-08-07 17:11:00,074 ] 52 root - INFO - Results relevant: False
[ 2025-08-07 17:11:00,076 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.4136 seconds.
[ 2025-08-07 17:11:00,078 ] 460 root - INFO - \u274c No results: 1.42s
[ 2025-08-07 17:11:00,080 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-07 17:11:14,895 ] 513 root - INFO - Enhanced web search query: Germany record as of 2025 as of 2025
[ 2025-08-07 17:11:14,897 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:11:14,898 ] 45 root - INFO - \U0001f310 Fast web search: 'Germany record as of 2025 as o...'
[ 2025-08-07 17:11:17,371 ] 59 root - INFO - \u26a1 Web search: 2.47s, 2 results
[ 2025-08-07 17:11:17,371 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4747 seconds.
[ 2025-08-07 17:11:17,373 ] 525 root - INFO - \U0001f310 Web search: 2.48s, 2 results
[ 2025-08-07 17:11:36,997 ] 513 root - INFO - Enhanced web search query: CPU comparison as of 2025 as of 2025
[ 2025-08-07 17:11:36,999 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:11:37,000 ] 45 root - INFO - \U0001f310 Fast web search: 'CPU comparison as of 2025 as o...'
[ 2025-08-07 17:11:39,707 ] 59 root - INFO - \u26a1 Web search: 2.71s, 2 results
[ 2025-08-07 17:11:39,711 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7124 seconds.
[ 2025-08-07 17:11:39,718 ] 525 root - INFO - \U0001f310 Web search: 2.72s, 2 results
[ 2025-08-07 17:11:40,691 ] 513 root - INFO - Enhanced web search query: CPU comparison Intel vs AMD as of 2025 as of 2025
[ 2025-08-07 17:11:40,692 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:11:40,693 ] 45 root - INFO - \U0001f310 Fast web search: 'CPU comparison Intel vs AMD as...'
[ 2025-08-07 17:11:41,785 ] 513 root - INFO - Enhanced web search query: latest news as of 2025 as of 2025
[ 2025-08-07 17:11:41,790 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:11:41,794 ] 45 root - INFO - \U0001f310 Fast web search: 'latest news as of 2025 as of 2...'
[ 2025-08-07 17:11:43,582 ] 59 root - INFO - \u26a1 Web search: 2.89s, 2 results
[ 2025-08-07 17:11:43,584 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8904 seconds.
[ 2025-08-07 17:11:43,584 ] 525 root - INFO - \U0001f310 Web search: 2.89s, 2 results
[ 2025-08-07 17:11:44,696 ] 59 root - INFO - \u26a1 Web search: 2.90s, 2 results
[ 2025-08-07 17:11:44,697 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9074 seconds.
[ 2025-08-07 17:11:44,699 ] 525 root - INFO - \U0001f310 Web search: 2.91s, 2 results
[ 2025-08-07 17:11:46,516 ] 513 root - INFO - Enhanced web search query: latest news as of 2025 as of 2025
[ 2025-08-07 17:11:46,520 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:11:46,521 ] 45 root - INFO - \U0001f310 Fast web search: 'latest news as of 2025 as of 2...'
[ 2025-08-07 17:11:49,464 ] 59 root - INFO - \u26a1 Web search: 2.94s, 2 results
[ 2025-08-07 17:11:49,465 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9450 seconds.
[ 2025-08-07 17:11:49,465 ] 525 root - INFO - \U0001f310 Web search: 2.95s, 2 results
[ 2025-08-07 17:11:51,033 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 17:11:59,344 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:12:00,818 ] 666 root - INFO - Call logging started for call ID: SCL_dQbMxmTaxdUS
[ 2025-08-07 17:12:06,472 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 17:12:06,487 ] 755 root - INFO - Call logging ended for call ID: SCL_dQbMxmTaxdUS
[ 2025-08-07 17:12:06,490 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 17:12:07,522 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 17:12:07,527 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 17:12:07,528 ] 513 root - INFO - Enhanced web search query: current weather as of 2025 as of 2025
[ 2025-08-07 17:12:07,530 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:12:07,530 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:12:07,534 ] 45 root - INFO - \U0001f310 Fast web search: 'current weather as of 2025 as ...'
[ 2025-08-07 17:12:07,540 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-07 17:12:07,551 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 17:12:07,567 ] 51 root - INFO - Vector DB search completed in 0.03s
[ 2025-08-07 17:12:07,578 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:12:07,587 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0564 seconds.
[ 2025-08-07 17:12:07,599 ] 457 root - INFO - \u2705 Vector search: 0.07s
[ 2025-08-07 17:12:10,444 ] 59 root - INFO - \u26a1 Web search: 2.91s, 2 results
[ 2025-08-07 17:12:10,447 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9165 seconds.
[ 2025-08-07 17:12:10,449 ] 525 root - INFO - \U0001f310 Web search: 2.93s, 2 results
[ 2025-08-07 17:12:11,394 ] 513 root - INFO - Enhanced web search query: journey person as of 2025 as of 2025
[ 2025-08-07 17:12:11,399 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:12:11,404 ] 45 root - INFO - \U0001f310 Fast web search: 'journey person as of 2025 as o...'
[ 2025-08-07 17:12:13,979 ] 59 root - INFO - \u26a1 Web search: 2.58s, 2 results
[ 2025-08-07 17:12:13,980 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5816 seconds.
[ 2025-08-07 17:12:13,981 ] 525 root - INFO - \U0001f310 Web search: 2.59s, 2 results
[ 2025-08-07 17:12:14,516 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:12:14,520 ] 37 root - INFO - \U0001f50d Vector DB search for: 'journeyperson...'
[ 2025-08-07 17:12:18,512 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:12:18,514 ] 159 root - INFO - \u26a1 New response generated in 3.99s
[ 2025-08-07 17:12:18,514 ] 51 root - INFO - Vector DB search completed in 3.99s
[ 2025-08-07 17:12:18,514 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:12:18,515 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.9990 seconds.
[ 2025-08-07 17:12:18,515 ] 457 root - INFO - \u2705 Vector search: 4.00s
[ 2025-08-07 17:12:20,875 ] 513 root - INFO - Enhanced web search query: Germany president as of 2025 as of 2025
[ 2025-08-07 17:12:20,878 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:12:20,880 ] 45 root - INFO - \U0001f310 Fast web search: 'Germany president as of 2025 a...'
[ 2025-08-07 17:12:23,102 ] 59 root - INFO - \u26a1 Web search: 2.22s, 2 results
[ 2025-08-07 17:12:23,103 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2243 seconds.
[ 2025-08-07 17:12:23,106 ] 525 root - INFO - \U0001f310 Web search: 2.23s, 2 results
[ 2025-08-07 17:12:23,638 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:12:23,639 ] 37 root - INFO - \U0001f50d Vector DB search for: 'Germany president...'
[ 2025-08-07 17:12:25,289 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:12:25,291 ] 159 root - INFO - \u26a1 New response generated in 1.64s
[ 2025-08-07 17:12:25,292 ] 51 root - INFO - Vector DB search completed in 1.65s
[ 2025-08-07 17:12:25,294 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:12:25,298 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.6596 seconds.
[ 2025-08-07 17:12:25,303 ] 457 root - INFO - \u2705 Vector search: 1.67s
[ 2025-08-07 17:12:38,982 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 17:13:15,474 ] 196 livekit.agents - ERROR - job shutdown is taking too much time..
[ 2025-08-07 17:14:07,880 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 17:14:08,144 ] 107 livekit.agents - WARNING - exiting forcefully
