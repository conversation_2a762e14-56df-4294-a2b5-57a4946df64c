// ConfigService: Handles API keys, endpoints, and feature flags

export type FeatureFlags = {
  enterpriseMode: boolean;
  multilingual: boolean;
  analytics: boolean;
  compliance: boolean;
};

export class ConfigService {
  private static instance: ConfigService;
  private featureFlags: FeatureFlags;
  private apiEndpoint: string;
  private aiApiKey: string;

  private constructor() {
    this.featureFlags = {
      enterpriseMode: true,
      multilingual: true,
      analytics: true,
      compliance: true,
    };
    this.apiEndpoint = process.env.REACT_APP_API_ENDPOINT || '';
    this.aiApiKey = process.env.REACT_APP_AI_API_KEY || '';
  }

  static getInstance() {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  getFeatureFlags() {
    return this.featureFlags;
  }

  getApiEndpoint() {
    return this.apiEndpoint;
  }

  getAIApiKey() {
    return this.aiApiKey;
  }
} 