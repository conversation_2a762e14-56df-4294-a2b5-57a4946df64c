[ 2025-08-11 17:07:03,317 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 17:07:03,327 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,328 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,328 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,329 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,329 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,330 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,331 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,332 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,337 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,337 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,337 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,339 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:03,351 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,351 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,353 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,353 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,353 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,353 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,354 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,354 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,354 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,354 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,355 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,355 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:03,887 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 17:07:12,181 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 17:07:12,294 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 17:07:12,355 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 17:07:12,356 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 17:07:12,745 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4507 seconds.
[ 2025-08-11 17:07:16,757 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 17:07:25,535 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 17:07:25,536 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 17:07:25,537 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 17:07:26,328 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 14.03 seconds
[ 2025-08-11 17:07:43,824 ] 282 root - INFO - \U0001f4f1 DTMF received: 1
[ 2025-08-11 17:07:43,824 ] 160 root - INFO - \U0001f30d Language instructions updated to: English
[ 2025-08-11 17:07:43,825 ] 190 root - ERROR - Failed to update TTS voice: property 'tts' of 'EnhancedCallSession' object has no setter
[ 2025-08-11 17:07:43,826 ] 195 root - INFO - \U0001f4dd Language instructions updated in session
[ 2025-08-11 17:07:43,826 ] 315 root - INFO - \U0001f30d Language locked to: English (en)
[ 2025-08-11 17:08:28,953 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 17:08:28,954 ] 341 root - INFO - Participant disconnected: sip_+916295716352
[ 2025-08-11 17:08:45,021 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 17:08:45,455 ] 107 livekit.agents - WARNING - exiting forcefully
