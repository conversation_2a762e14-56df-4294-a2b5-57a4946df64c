
import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Shield, CheckCircle, AlertTriangle, Download, Calendar } from 'lucide-react';

export const ComplianceStatus = () => {
  const complianceItems = [
    {
      name: 'GDPR',
      status: 'Compliant',
      lastAudit: '2024-01-10',
      nextAudit: '2024-04-10',
      isCompliant: true
    },
    {
      name: 'SOC 2 Type II',
      status: 'Compliant',
      lastAudit: '2024-01-15',
      nextAudit: '2024-07-15',
      isCompliant: true
    },
    {
      name: 'ISO/IEC 27001',
      status: 'Compliant',
      lastAudit: '2023-12-20',
      nextAudit: '2024-12-20',
      isCompliant: true
    },
    {
      name: 'HIPAA',
      status: 'Under Review',
      lastAudit: '2024-01-05',
      nextAudit: '2024-03-05',
      isCompliant: false
    },
    {
      name: 'DORA',
      status: 'Compliant',
      lastAudit: '2024-01-12',
      nextAudit: '2024-06-12',
      isCompliant: true
    },
    {
      name: 'EU AI Act',
      status: 'Compliant',
      lastAudit: '2024-01-08',
      nextAudit: '2024-04-08',
      isCompliant: true
    }
  ];

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Shield className="w-6 h-6 text-blue-400" />
          <h3 className="text-xl font-semibold text-white">Compliance Status</h3>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Audit
          </Button>
          <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500">
            <Download className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {complianceItems.map((item, index) => (
          <div key={index} className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-white">{item.name}</h4>
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                item.isCompliant 
                  ? 'bg-green-500/20 text-green-400' 
                  : 'bg-yellow-500/20 text-yellow-400'
              }`}>
                {item.isCompliant ? (
                  <CheckCircle className="w-3 h-3" />
                ) : (
                  <AlertTriangle className="w-3 h-3" />
                )}
                <span>{item.status}</span>
              </div>
            </div>
            <div className="space-y-2 text-sm text-gray-400">
              <div>Last Audit: {item.lastAudit}</div>
              <div>Next Audit: {item.nextAudit}</div>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
};
