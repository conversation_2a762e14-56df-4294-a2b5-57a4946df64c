import os
import sys
import asyncio
from typing import Dict, Any, Optional
from concurrent.futures import Thread<PERSON>oolExecutor
from functools import lru_cache
import time
import json
from datetime import datetime
from pathlib import Path
from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.logging.call_logger import call_logger
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm, stt
from livekit.plugins import silero, groq, speechify
from livekit import rtc
from dotenv import load_dotenv

load_dotenv()

# Configuration loading
def load_model_config():
    """Load model configuration from saved settings"""
    config_file = Path("config/model_settings.json")
    if config_file.exists():
        try:
            with open(config_file, 'r') as f:
                config = json.load(f)
                logging.info(f"Loaded model configuration: {config}")
                return config
        except Exception as e:
            logging.error(f"Error loading model config: {e}")

    # Default configuration
    default_config = {
        "stt": {"model": "whisper-large-v3", "api_key": os.getenv("GROQ_API_KEY", "")},
        "llm": {"model": "llama3-70b-8192", "api_key": os.getenv("GROQ_API_KEY", "")},
        "tts": {"model": "simba-multilingual", "api_key": os.getenv("SPEECHIFY_API_KEY", "")}
    }
    logging.info(f"Using default model configuration: {default_config}")
    return default_config

# Load configuration at startup
MODEL_CONFIG = load_model_config()

# Global variables for session language tracking and call logging
session_language = None
session_language_config = None
current_call_id = None
transcription_logger = None

# Add missing imports and global variables
rag_agent = None
language_detector = None

# Initialize RAG agent and language detector at startup for fast access
print("🚀 Initializing RAG agent and language detector at startup...")
startup_time = time.time()
try:
    rag_agent = AgenticRAG()
    language_detector = LanguageDetector()
    startup_duration = time.time() - startup_time
    print(f"✅ RAG agent and language detector initialized successfully in {startup_duration:.2f}s!")
except Exception as e:
    print(f"⚠️ Failed to initialize RAG agent: {e}")
    rag_agent = None
    language_detector = None

@lru_cache(maxsize=1000)
def cached_language_detection(query_hash: str, query: str):
    """Cached language detection for ultra-fast lookup"""
    try:
        global language_detector
        if language_detector is None:
            language_detector = LanguageDetector()
        return language_detector.detect_language(query)
    except Exception as e:
        logging.error(f"Language detection error: {e}")
        return 'en', 'English', {'name': 'English'}

class TranscriptionLogger:
    """Enhanced logger for user and agent transcriptions during calls"""

    def __init__(self, log_directory: str = "call_logs"):
        self.log_directory = log_directory
        # Ensure directory exists
        os.makedirs(self.log_directory, exist_ok=True)
        self.call_transcript_file = None
        self.console_transcript_file = None
        self.current_file = None

    def start_call_transcription(self, call_id: str):
        """Start logging transcriptions for a call"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"call_transcript_{call_id}_{timestamp}.txt"
        self.call_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.call_transcript_file

        with open(self.call_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CALL TRANSCRIPTION LOG ===\n")
            f.write(f"Call ID: {call_id}\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started call transcription logging: {filename}")

    def start_console_transcription(self):
        """Start logging transcriptions for console testing"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"console_transcript_{timestamp}.txt"
        self.console_transcript_file = os.path.join(self.log_directory, filename)
        self.current_file = self.console_transcript_file

        with open(self.console_transcript_file, 'w', encoding='utf-8') as f:
            f.write(f"=== CONSOLE TRANSCRIPTION LOG ===\n")
            f.write(f"Started: {datetime.now().isoformat()}\n")
            f.write("=" * 50 + "\n\n")

        print(f"📝 Started console transcription logging: {filename}")

    def log_transcription(self, text: str | list, is_final: bool = True, speaker: str = "USER"):
        """Log transcription to appropriate file"""
        # Convert list to string if needed
        if isinstance(text, list):
            text = " ".join(str(item) for item in text)
        elif not isinstance(text, str):
            text = str(text)

        if not text or not text.strip():
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        # Format the log entry with clear speaker identification
        speaker_emoji = "👤" if speaker == "USER" else "🤖"
        log_entry = f"[{timestamp}] {speaker_emoji} {speaker}: {text.strip()}\n"

        # Determine which file to write to
        target_file = self.current_file

        if target_file and is_final:
            try:
                with open(target_file, 'a', encoding='utf-8') as f:
                    f.write(log_entry)
                    f.flush()  # Ensure immediate write

                # Also print to console for real-time monitoring
                print(f"📝 {speaker_emoji} {speaker}: {text.strip()}")

            except Exception as e:
                print(f"❌ Error writing transcription: {e}")

    def log_agent_response(self, text: str):
        """Convenience method to log agent responses"""
        self.log_transcription(text, is_final=True, speaker="AGENT")

    def log_user_speech(self, text: str):
        """Convenience method to log user speech"""
        self.log_transcription(text, is_final=True, speaker="USER")

    def close_transcription(self):
        """Close the current transcription session"""
        if self.current_file:
            try:
                with open(self.current_file, 'a', encoding='utf-8') as f:
                    f.write(f"\n=== TRANSCRIPTION ENDED ===\n")
                    f.write(f"Ended: {datetime.now().isoformat()}\n")
                print(f"📝 Transcription session closed: {self.current_file}")
            except Exception as e:
                print(f"❌ Error closing transcription: {e}")

class EnhancedCallLoggingSession(AgentSession):
    """Enhanced session with transcription logging and proper call lifecycle management"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.call_active = False

    async def start(self, agent, room):
        """Override start to properly manage call lifecycle"""
        global current_call_id, transcription_logger

        # Set up comprehensive transcription event handlers
        @self.on("user_speech_committed")
        def on_user_speech(event):
            """Handle user speech transcription"""
            if transcription_logger and hasattr(event, 'user_transcript') and event.user_transcript:
                transcription_logger.log_user_speech(event.user_transcript)

                # Also log to call logger if we have a call ID
                if current_call_id:
                    call_logger.log_user_transcription(current_call_id, event.user_transcript)

        @self.on("agent_speech_committed")
        def on_agent_speech(event):
            """Handle agent speech transcription"""
            if transcription_logger and hasattr(event, 'agent_transcript') and event.agent_transcript:
                transcription_logger.log_agent_response(event.agent_transcript)

                # Also log to call logger if we have a call ID
                if current_call_id:
                    call_logger.log_agent_response(current_call_id, event.agent_transcript)

        # Additional event handlers for comprehensive logging
        @self.on("conversation_item_added")
        def on_conversation_item(event):
            """Handle conversation items (covers both user and agent messages)"""
            if transcription_logger and hasattr(event, 'item'):
                item = event.item
                if hasattr(item, 'content') and item.content:
                    # Determine speaker based on item type or role
                    speaker = "USER" if hasattr(item, 'role') and item.role == 'user' else "AGENT"
                    transcription_logger.log_transcription(item.content, is_final=True, speaker=speaker)

        # Mark call as active
        self.call_active = True

        # Start the session
        await super().start(agent=agent, room=room)
    
    async def generate_reply(self, **kwargs):
        """Override to capture agent responses and maintain call state"""
        global current_call_id, transcription_logger

        # Generate the reply using parent method
        reply = await super().generate_reply(**kwargs)

        # Enhanced logging for agent responses
        if reply:
            response_text = None

            # Try different ways to extract the response text
            if hasattr(reply, 'content') and reply.content:
                response_text = reply.content
            elif hasattr(reply, 'text') and reply.text:
                response_text = reply.text
            elif isinstance(reply, str):
                response_text = reply

            # Log the agent response if we found text
            if response_text and transcription_logger:
                transcription_logger.log_agent_response(response_text)

            if response_text and current_call_id:
                call_logger.log_agent_response(current_call_id, response_text)

        return reply
    
    async def wait_for_completion(self):
        """Override to properly handle call completion"""
        global transcription_logger
        try:
            await super().wait_for_completion()
        finally:
            self.call_active = False
            # Close transcription logging when session ends
            if transcription_logger:
                transcription_logger.close_transcription()

def get_rag_agent():
    """Get preloaded RAG agent (should already be initialized at startup)"""
    global rag_agent
    if rag_agent is None:
        print("⚠️ RAG agent not preloaded, initializing now (this may cause delay)...")
        rag_agent = AgenticRAG()
    return rag_agent

def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    """Generate language-specific instructions for the agent."""
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

🚨 CRITICAL TOOL SELECTION RULES 🚨

1. MANDATORY WEB SEARCH - Use web_search tool for:
   - ANY question about Chief Minister of ANY state (e.g., "Who is the Chief Minister of Tamil Nadu?", "Current CM of Maharashtra")
   - ANY question about Prime Minister of ANY country (e.g., "Who is the Prime Minister of India?")
   - Current events, breaking news, recent developments
   - Weather updates and real-time information
   - ANY query containing words: "current", "recent", "latest", "now", "today", "2025"
   - When you need up-to-date information

2. MANDATORY VECTOR SEARCH - Use vector_database_search tool for:
   - ABB switchgear technical questions
   - Transformer specifications and details
   - RAG (Retrieval-Augmented Generation) concepts
   - NLP (Natural Language Processing) topics
   - Technical documentation queries
   - Company policies and procedures

3. NO TOOLS NEEDED for:
   - Simple greetings (hello, hi, how are you, what's your name)
   - Basic conversational responses

🎯 LANGUAGE CONSISTENCY:
   - Maintain English throughout the entire conversation
   - Never switch languages once English is detected

🔍 WEB SEARCH QUERY FORMAT:
   - Always append "as of 2025" to web search queries for current information
   - Example: "Chief Minister of Tamil Nadu as of 2025"

TOOLS AVAILABLE:
- vector_database_search: For technical documentation (ABB, transformers, RAG, NLP)
- web_search: For current officials, news, real-time data (always add "as of 2025")
"""

    # For non-English languages, get configuration
    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful voice AI assistant.

🚨 ABSOLUTE LANGUAGE RULES 🚨
- You MUST respond ONLY in {lang_name} using English alphabet transliteration
- NEVER use English words or switch languages during the session
- Example greeting: '{sample_greeting}'
- Maintain {lang_name} throughout the entire conversation

🚨 CRITICAL TOOL SELECTION RULES 🚨

1. MANDATORY WEB SEARCH - Use web_search tool for:
   - ANY question about Chief Minister of ANY state
   - ANY question about Prime Minister of ANY country  
   - Current events, breaking news, recent developments
   - Weather updates and real-time information
   - ANY query containing words: "current", "recent", "latest", "now", "today", "2025"
   - When you need up-to-date information

2. MANDATORY VECTOR SEARCH - Use vector_database_search tool for:
   - ABB switchgear technical questions
   - Transformer specifications and details
   - RAG (Retrieval-Augmented Generation) concepts
   - NLP (Natural Language Processing) topics
   - Technical documentation queries
   - Company policies and procedures

3. NO TOOLS NEEDED for:
   - Simple greetings and basic conversations

🔍 WEB SEARCH QUERY FORMAT:
   - Always append "as of 2025" to web search queries for current information
   - Convert response to {lang_name} transliteration

🎯 RESPONSE FORMAT:
   - ALL responses must be in {lang_name} transliteration using English alphabet
   - Keep responses conversational and concise
   - Never mix languages

TOOLS AVAILABLE:
- vector_database_search: For technical documentation (ABB, transformers, RAG, NLP)
- web_search: For current officials, news, real-time data (always add "as of 2025")

EXAMPLES:
- Greeting: "{sample_greeting}" (NO TOOLS)
- "Chief Minister kaun hai?" → web_search with "as of 2025"
- "ABB switchgear kya hai?" → vector_database_search
- Technical query → vector_database_search
- Current events → web_search with "as of 2025"
"""

# Pre-create tools for faster access using preloaded RAG agent
print("🔧 Pre-creating tools with preloaded RAG agent...")
try:
    if rag_agent is not None:
        vector_tool_instance = VectorDatabaseTool(rag_agent.llm)
        print("✅ Vector database tool created with preloaded RAG agent")
    else:
        print("⚠️ RAG agent not available, vector tool will be created on demand")
        vector_tool_instance = None

    web_tool_instance = WebSearchTool()
    print("✅ Web search tool created")
    print("✅ All tools pre-created successfully")
except Exception as e:
    print(f"⚠️ Tool pre-creation failed: {e}")
    vector_tool_instance = None
    web_tool_instance = None

# Pre-compiled language responses for ultra-fast lookup
FAST_RESPONSES = {
    'hi': {
        'no_results': "Mere paas is technical sawal ka jawaab nahi hai.",
        'error': "Knowledge base mein kuch technical problem hai."
    },
    'ta': {
        'no_results': "Enakku indha technical kelvikku information illa.",
        'error': "Knowledge base la konjam technical problem irukku."
    },
    'te': {
        'no_results': "Naa daggara ee technical prashnaku information ledu.",
        'error': "Knowledge base lo konni technical samasyalu unnaayi."
    },
    'de': {
        'no_results': "Ich habe keine technischen Informationen dazu.",
        'error': "Es gibt ein technisches Problem mit der Wissensdatenbank."
    },
    'fr': {
        'no_results': "Je n'ai pas d'informations techniques à ce sujet.",
        'error': "Il y a un problème technique avec la base de connaissances."
    },
    'en': {
        'no_results': "I don't have technical information about this.",
        'error': "Technical issue with knowledge base."
    }
}

def create_vector_database_tool():
    """Create an ultra-fast vector database search tool."""

    @llm.function_tool(
        name="vector_database_search",
        description="MANDATORY for: ABB switchgear, transformers, RAG (Retrieval-Augmented Generation), NLP (Natural Language Processing), technical documentation, company policies. NEVER use for current officials or real-time information."
    )
    async def vector_database_search(query: str) -> str:
        """Search internal vector database for technical documentation."""
        start_time = time.time()

        try:
            global session_language, session_language_config, vector_tool_instance

            # Use preloaded vector tool instance (should already be available)
            if vector_tool_instance is None:
                print("⚠️ Vector tool not preloaded, creating on demand...")
                rag = get_rag_agent()
                vector_tool_instance = VectorDatabaseTool(rag.llm)

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # Parallel search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                vector_tool_instance.search_documents,
                query,
                session_language or 'en'
            )

            # Ultra-fast response selection
            lang = session_language or 'en'
            if result['is_relevant']:
                response = result['results']
                logging.info(f"✅ Vector search: {time.time() - start_time:.2f}s")
            else:
                response = FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['no_results']
                logging.info(f"❌ No results: {time.time() - start_time:.2f}s")

            # Language enforcement for non-English
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = f"[{lang_name} response] {response}"

            # Log the tool response as agent response
            global current_call_id, transcription_logger
            if transcription_logger:
                transcription_logger.log_agent_response(response)
            if current_call_id:
                call_logger.log_agent_response(current_call_id, response)

            return response

        except Exception as e:
            logging.error(f"Vector tool error: {e}")
            lang = session_language or 'en'
            return FAST_RESPONSES.get(lang, FAST_RESPONSES['en'])['error']

    return vector_database_search

def create_web_search_tool():
    """Create an ultra-fast web search tool."""

    @llm.function_tool(
        name="web_search",
        description="MANDATORY for: Chief Minister queries, Prime Minister queries, current events, recent news, weather, real-time information. ALWAYS append 'as of 2025' to queries for accurate current information."
    )
    async def web_search(query: str) -> str:
        """Search the web for current information.and information related to chief misters and prime ministers."""
        start_time = time.time()

        try:
            global session_language, session_language_config, web_tool_instance

            # Use preloaded web tool instance (should already be available)
            if web_tool_instance is None:
                print("⚠️ Web tool not preloaded, creating on demand...")
                web_tool_instance = WebSearchTool()

            # Ultra-fast language detection with caching
            if session_language is None:
                query_hash = str(hash(query))
                lang_code, lang_name, lang_config = cached_language_detection(query_hash, query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"⚡ Fast language detection: {lang_name} ({lang_code})")

            # CRITICAL: Always append "as of 2025" for current information
            enhanced_query = f"{query} as of 2025"
            logging.info(f"Enhanced web search query: {enhanced_query}")

            # Parallel web search execution
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                web_tool_instance.search_web,
                enhanced_query,
                session_language or 'en'
            )

            response = result['results']
            logging.info(f"🌐 Web search: {time.time() - start_time:.2f}s, {result['result_count']} results")

            # Language enforcement for non-English
            lang = session_language or 'en'
            if lang != 'en' and session_language_config:
                lang_name = session_language_config.get('name', '')
                if lang_name:
                    response = f"[{lang_name} response] {response}"

            # Log the tool response as agent response
            global current_call_id, transcription_logger
            if transcription_logger:
                transcription_logger.log_agent_response(response)
            if current_call_id:
                call_logger.log_agent_response(current_call_id, response)

            return response

        except Exception as e:
            logging.error(f"Web search error: {e}")
            lang = session_language or 'en'
            # Fast error responses
            web_errors = {
                'hi': "Web search mein technical problem hai.",
                'ta': "Web search la technical problem irukku.",
                'te': "Web search lo technical problem undi.",
                'de': "Technisches Problem bei der Websuche.",
                'fr': "Problème technique avec la recherche web.",
                'en': "Technical issue with web search."
            }
            return web_errors.get(lang, web_errors['en'])

    return web_search

class UltraFastLanguageAgent(Agent):
    """Ultra-optimized Agent with lightning-fast language detection and response."""

    def __init__(self, llm, tools):
        # Initial language selection prompt
        initial_instructions = """⚡ MULTILINGUAL AI ASSISTANT ⚡

🌍 LANGUAGE SELECTION:
Namaste! Hindi ke liye "Hindi" boliye
Hello! For English say "English"  
Vanakkam! Tamil ke liye "Tamil" sollunga
Namaskaram! Telugu kosam "Telugu" cheppandi
Bonjour! Pour le français dites "French"
Hallo! Für Deutsch sagen Sie "German"

Please select your preferred language first, then I will respond only in that language using English alphabet pronunciation.

🚨 TOOL USAGE RULES:
- Current information (CM, PM, news, weather) → web_search (with "as of 2025")
- Technical documentation (ABB, transformers, RAG, NLP) → vector_database_search
- Simple greetings → Direct response (no tools)

Waiting for your language selection...
"""
        
        super().__init__(
            llm=llm,
            tools=tools,
            instructions=initial_instructions
        )
        
        self.current_language = None
        self.language_config = None
        self.language_locked = False

    async def _handle_user_message(self, message):
        """Handle user messages with ultra-fast language detection and tool routing."""
        start_time = time.time()

        global session_language, session_language_config, current_call_id, transcription_logger

        if message.content:
            # Enhanced user transcription logging
            if transcription_logger:
                transcription_logger.log_user_speech(message.content)

            # Log user transcription to call logger
            if current_call_id:
                call_logger.log_user_transcription(current_call_id, message.content)

            # Lightning-fast language detection with caching
            if session_language is None:
                msg_hash = str(hash(message.content))
                lang_code, lang_name, lang_config = cached_language_detection(msg_hash, message.content)

                if lang_code != 'unknown':
                    session_language = lang_code
                    session_language_config = lang_config
                    self.current_language = lang_code
                    self.language_config = lang_config
                    self.language_locked = True
                    logging.info(f"⚡ LANGUAGE LOCKED: {lang_name} ({lang_code}) in {time.time() - start_time:.3f}s")

                    # Log detected language
                    if current_call_id:
                        call_logger.set_language(current_call_id, f"{lang_name} ({lang_code})")

                    # Update instructions for the detected language
                    self.instructions = get_language_specific_instructions(lang_code, lang_config)
            else:
                lang_code = session_language
                lang_name = session_language_config.get('name', 'Unknown') if session_language_config else 'Unknown'
                logging.info(f"🔒 USING LOCKED LANGUAGE: {lang_name}")

        return await super()._handle_user_message(message)

async def entrypoint(ctx: JobContext):
    """Enhanced entrypoint with proper call lifecycle and transcription logging."""
    global session_language, session_language_config, current_call_id, transcription_logger

    try:
        print("🚀 Starting enhanced voice agent with transcription logging...")
        start_time = time.time()

        await ctx.connect()

        # Reset session variables
        session_language = None
        session_language_config = None
        current_call_id = None
        
        # Initialize transcription logger
        transcription_logger = TranscriptionLogger()

        # Wait for participant
        print("⏳ Waiting for participant...")
        participant = await ctx.wait_for_participant()

        # Check if this is a SIP call and initialize logging
        is_sip_call = hasattr(participant, 'kind') and participant.kind == rtc.ParticipantKind.PARTICIPANT_KIND_SIP
        
        if is_sip_call:
            current_call_id = call_logger.start_call(participant)
            if current_call_id:
                print(f"📞 SIP Call detected - Call ID: {current_call_id}")
                print(f"📱 Caller: {participant.attributes.get('sip.phoneNumber', 'Unknown')}")
                transcription_logger.start_call_transcription(current_call_id)
                logging.info(f"Call logging started for call ID: {current_call_id}")
            else:
                print("⚠️ Failed to start call logging")
        else:
            print("🖥️ Console/Web participant detected - Starting console transcription")
            transcription_logger.start_console_transcription()

        # Verify preloaded components
        if rag_agent is not None:
            print("✅ Using preloaded RAG agent - no initialization delay!")
        else:
            print("⚠️ RAG agent not preloaded - may experience delays during first use")

        if vector_tool_instance is not None:
            print("✅ Using preloaded vector database tool")
        else:
            print("⚠️ Vector tool not preloaded")

        if web_tool_instance is not None:
            print("✅ Using preloaded web search tool")
        else:
            print("⚠️ Web tool not preloaded")

        # Create tools (these should use preloaded instances)
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()

        # Create enhanced session with dynamic model configuration
        stt_model = MODEL_CONFIG.get("stt", {}).get("model", "whisper-large-v3")
        llm_model = MODEL_CONFIG.get("llm", {}).get("model", "llama3-70b-8192")
        tts_model = MODEL_CONFIG.get("tts", {}).get("model", "simba-multilingual")

        # Set API keys from configuration
        stt_api_key = MODEL_CONFIG.get("stt", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        llm_api_key = MODEL_CONFIG.get("llm", {}).get("api_key") or os.getenv("GROQ_API_KEY")
        tts_api_key = MODEL_CONFIG.get("tts", {}).get("api_key") or os.getenv("SPEECHIFY_API_KEY")

        print(f"🎤 Using STT model: {stt_model}")
        print(f"🧠 Using LLM model: {llm_model}")
        print(f"🔊 Using TTS model: {tts_model}")

        session = EnhancedCallLoggingSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model=stt_model,
                detect_language=True,
                api_key=stt_api_key
            ),
            llm=groq.LLM(
                model=llm_model,
                temperature=0.05,
                api_key=llm_api_key
            ),
            tts=speechify.TTS(
                model=tts_model,
                api_key=tts_api_key
            ),
        )

        # Create agent
        agent = UltraFastLanguageAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )

        # Start session
        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Agent ready in {time.time() - start_time:.2f}s")

        # Send initial greeting
        await session.generate_reply(
            instructions="Say: 'Hello! Ready to help!' Keep it under 5 words."
        )

        # Keep the session alive and wait for completion
        print("🎙️ Session active - waiting for conversation...")
        await session.wait_for_completion()

    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)
    finally:
        # Properly end call logging and transcription when session ends
        if transcription_logger:
            transcription_logger.close_transcription()
        if current_call_id:
            call_logger.end_call(current_call_id)
            print(f"📞 Call logging ended for call ID: {current_call_id}")
            logging.info(f"Call logging ended for call ID: {current_call_id}")

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint,agent_name="telephony_agent"))









