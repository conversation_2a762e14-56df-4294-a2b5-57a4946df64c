
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Database, 
  User, 
  MapPin, 
  Clock, 
  Shield, 
  Eye, 
  FileText,
  Phone,
  Mic,
  MessageSquare,
  Heart,
  Brain,
  Lock,
  Server,
  Globe,
  AlertTriangle,
  CheckCircle,
  Download
} from 'lucide-react';

export const DataProcessingDetails = () => {
  const [selectedDataType, setSelectedDataType] = useState<string>('voice');

  const dataTypes = {
    voice: {
      name: 'Voice & Audio Data',
      icon: <Mic className="w-5 h-5" />,
      description: 'Voice recordings, audio patterns, speech characteristics',
      legalBasis: 'Consent (Art. 6(1)(a) GDPR) + Legitimate Interest (Art. 6(1)(f))',
      retention: '30 days (auto-deletion)',
      processing: [
        'Real-time speech-to-text conversion',
        'Voice pattern analysis for identity verification',
        'Emotion detection from voice tone',
        'Language detection and translation',
        'Audio quality enhancement'
      ],
      storage: 'Encrypted in EU-based servers (Frankfurt, Germany)',
      sharing: 'Not shared with third parties',
      rights: ['Access', 'Rectification', 'Erasure', 'Portability', 'Objection'],
      compliance: {
        gdpr: 'Full compliance - explicit consent required',
        euAiAct: 'High-risk AI system - human oversight implemented',
        hipaa: 'N/A unless healthcare context',
        dora: 'Operational resilience measures in place'
      }
    },
    personal: {
      name: 'Personal Identifiers',
      icon: <User className="w-5 h-5" />,
      description: 'Name, email, phone number, customer ID',
      legalBasis: 'Contract necessity (Art. 6(1)(b) GDPR)',
      retention: '5 years (business records)',
      processing: [
        'Customer identification and authentication',
        'Call routing and personalization',
        'Service delivery and support',
        'Communication and follow-up',
        'Legal compliance and record keeping'
      ],
      storage: 'Encrypted database in EU (GDPR Art. 32)',
      sharing: 'Only with authorized service providers under DPA',
      rights: ['Access', 'Rectification', 'Erasure', 'Portability', 'Restriction'],
      compliance: {
        gdpr: 'Lawful basis established - contract performance',
        euAiAct: 'Transparency obligations met',
        hipaa: 'PHI protection if healthcare data involved',
        dora: 'ICT risk management applied'
      }
    },
    conversation: {
      name: 'Conversation Content',
      icon: <MessageSquare className="w-5 h-5" />,
      description: 'Transcripts, conversation history, intent analysis',
      legalBasis: 'Legitimate Interest (Art. 6(1)(f) GDPR)',
      retention: '90 days (service improvement)',
      processing: [
        'Intent recognition and response generation',
        'Conversation flow optimization',
        'Quality assurance and training',
        'Customer service analytics',
        'AI model improvement (anonymized)'
      ],
      storage: 'Pseudonymized in secure EU cloud',
      sharing: 'Anonymized data for AI training only',
      rights: ['Access', 'Rectification', 'Erasure', 'Objection'],
      compliance: {
        gdpr: 'Legitimate interest assessment documented',
        euAiAct: 'AI transparency and explainability provided',
        hipaa: 'Special category data protection',
        dora: 'Data governance framework implemented'
      }
    },
    behavioral: {
      name: 'Behavioral Analytics',
      icon: <Brain className="w-5 h-5" />,
      description: 'Interaction patterns, preferences, usage statistics',
      legalBasis: 'Legitimate Interest (Art. 6(1)(f) GDPR)',
      retention: '1 year (analytics)',
      processing: [
        'Service personalization and improvement',
        'User experience optimization',
        'Predictive analytics for better service',
        'Performance monitoring and reporting',
        'Statistical analysis (aggregated)'
      ],
      storage: 'Anonymized in analytics platform (EU)',
      sharing: 'Aggregated statistics only (no personal data)',
      rights: ['Access', 'Objection', 'Restriction'],
      compliance: {
        gdpr: 'Data minimization principle applied',
        euAiAct: 'Automated decision-making safeguards',
        hipaa: 'De-identification standards met',
        dora: 'Operational monitoring capabilities'
      }
    },
    emotional: {
      name: 'Emotional Intelligence Data',
      icon: <Heart className="w-5 h-5" />,
      description: 'Emotion detection, sentiment analysis, stress indicators',
      legalBasis: 'Explicit Consent (Art. 9(2)(a) GDPR - Special Category)',
      retention: '30 days (immediate processing only)',
      processing: [
        'Real-time emotion detection for better service',
        'Stress level monitoring for escalation',
        'Sentiment analysis for quality improvement',
        'Empathetic response generation',
        'Customer satisfaction prediction'
      ],
      storage: 'Highly encrypted, EU-only, access-controlled',
      sharing: 'Never shared - strictly internal use',
      rights: ['Access', 'Rectification', 'Erasure', 'Portability', 'Objection', 'Withdraw Consent'],
      compliance: {
        gdpr: 'Special category data - explicit consent required',
        euAiAct: 'High-risk AI - conformity assessment required',
        hipaa: 'Mental health data protection protocols',
        dora: 'Enhanced security measures for sensitive data'
      }
    }
  };

  const complianceFrameworks = {
    gdpr: {
      name: 'GDPR (General Data Protection Regulation)',
      status: 'Fully Compliant',
      lastAudit: '2024-01-15',
      measures: [
        'Data Protection Impact Assessment (DPIA) completed',
        'Privacy by Design implementation',
        'Data Processing Agreements with all processors',
        'Regular staff training on data protection',
        'Incident response procedures established',
        'Data subject rights procedures implemented'
      ]
    },
    euAiAct: {
      name: 'EU AI Act',
      status: 'Compliant (High-Risk System)',
      lastAudit: '2024-01-10',
      measures: [
        'AI system risk assessment completed',
        'Human oversight mechanisms implemented',
        'Transparency and explainability features',
        'Accuracy and robustness testing',
        'Bias detection and mitigation',
        'Quality management system established'
      ]
    },
    hipaa: {
      name: 'HIPAA (Healthcare Data)',
      status: 'Ready for Healthcare Use',
      lastAudit: '2024-01-12',
      measures: [
        'Business Associate Agreements (BAA) framework',
        'PHI encryption and access controls',
        'Audit logging for all PHI access',
        'Risk assessment for healthcare scenarios',
        'Breach notification procedures',
        'Healthcare staff training protocols'
      ]
    },
    dora: {
      name: 'DORA (Digital Operational Resilience)',
      status: 'Compliant',
      lastAudit: '2024-01-08',
      measures: [
        'ICT risk management framework',
        'Incident reporting mechanisms',
        'Digital operational resilience testing',
        'Third-party risk assessment',
        'Information sharing protocols',
        'Threat-led penetration testing'
      ]
    }
  };

  return (
    <div className="space-y-6">
      <Card className="p-6 bg-gray-800/50 border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-semibold text-white">Data Processing Compliance Dashboard</h3>
            <Badge variant="outline" className="text-green-400 border-green-400">
              Enterprise Grade
            </Badge>
          </div>
          <Button variant="outline" size="sm" className="text-white border-gray-600">
            <Download className="w-4 h-4 mr-2" />
            Export Compliance Report
          </Button>
        </div>

        <Tabs defaultValue="data-types" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-gray-900/50">
            <TabsTrigger value="data-types" className="text-white">Data Types</TabsTrigger>
            <TabsTrigger value="compliance" className="text-white">Compliance Status</TabsTrigger>
            <TabsTrigger value="audit-trail" className="text-white">Audit Trail</TabsTrigger>
          </TabsList>

          <TabsContent value="data-types" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <h4 className="text-lg font-medium text-white">Data Categories</h4>
                {Object.entries(dataTypes).map(([key, data]) => (
                  <div
                    key={key}
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${
                      selectedDataType === key
                        ? 'bg-blue-600/20 border border-blue-500'
                        : 'bg-gray-900/50 border border-gray-700 hover:border-gray-600'
                    }`}
                    onClick={() => setSelectedDataType(key)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="text-blue-400">{data.icon}</div>
                      <div>
                        <h5 className="font-medium text-white">{data.name}</h5>
                        <p className="text-sm text-gray-400">{data.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                {selectedDataType && (
                  <div className="p-4 bg-gray-900/50 rounded-lg border border-gray-700">
                    <h5 className="text-lg font-medium text-white mb-4">
                      {dataTypes[selectedDataType as keyof typeof dataTypes].name} - Processing Details
                    </h5>
                    
                    <div className="space-y-4">
                      <div>
                        <span className="text-sm font-medium text-gray-300">Legal Basis:</span>
                        <p className="text-sm text-white mt-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].legalBasis}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-300">Retention Period:</span>
                        <p className="text-sm text-white mt-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].retention}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-300">Processing Activities:</span>
                        <ul className="text-sm text-white mt-1 space-y-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].processing.map((activity, idx) => (
                            <li key={idx} className="flex items-start space-x-2">
                              <span className="text-blue-400 mt-1">•</span>
                              <span>{activity}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-300">Storage & Security:</span>
                        <p className="text-sm text-white mt-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].storage}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-300">Data Sharing:</span>
                        <p className="text-sm text-white mt-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].sharing}
                        </p>
                      </div>

                      <div>
                        <span className="text-sm font-medium text-gray-300">Data Subject Rights:</span>
                        <div className="flex flex-wrap gap-2 mt-1">
                          {dataTypes[selectedDataType as keyof typeof dataTypes].rights.map((right, idx) => (
                            <Badge key={idx} variant="outline" className="text-green-400 border-green-400 text-xs">
                              {right}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="compliance" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {Object.entries(complianceFrameworks).map(([key, framework]) => (
                <Card key={key} className="p-4 bg-gray-900/50 border-gray-700">
                  <div className="flex items-center justify-between mb-3">
                    <h5 className="font-medium text-white">{framework.name}</h5>
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-4 h-4 text-green-400" />
                      <span className="text-sm text-green-400">{framework.status}</span>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <div className="text-sm text-gray-400">
                      Last Audit: {framework.lastAudit}
                    </div>
                    
                    <div>
                      <span className="text-sm font-medium text-gray-300">Compliance Measures:</span>
                      <ul className="text-xs text-gray-400 mt-1 space-y-1">
                        {framework.measures.map((measure, idx) => (
                          <li key={idx} className="flex items-start space-x-2">
                            <CheckCircle className="w-3 h-3 text-green-400 mt-0.5 flex-shrink-0" />
                            <span>{measure}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="audit-trail" className="space-y-4">
            <Card className="p-4 bg-gray-900/50 border-gray-700">
              <h5 className="font-medium text-white mb-4">Recent Compliance Activities</h5>
              <div className="space-y-3">
                {[
                  { date: '2024-01-15', action: 'DPIA completed for emotion detection feature', user: '<EMAIL>', status: 'Approved' },
                  { date: '2024-01-14', action: 'Data retention policy updated', user: '<EMAIL>', status: 'Implemented' },
                  { date: '2024-01-13', action: 'Third-party processor agreement signed', user: '<EMAIL>', status: 'Active' },
                  { date: '2024-01-12', action: 'User consent mechanism updated', user: '<EMAIL>', status: 'Deployed' },
                  { date: '2024-01-11', action: 'Security audit completed', user: '<EMAIL>', status: 'Passed' }
                ].map((activity, idx) => (
                  <div key={idx} className="flex items-center justify-between p-3 bg-gray-800/50 rounded">
                    <div>
                      <div className="text-sm text-white">{activity.action}</div>
                      <div className="text-xs text-gray-400">by {activity.user} on {activity.date}</div>
                    </div>
                    <Badge variant="outline" className="text-green-400 border-green-400 text-xs">
                      {activity.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </Card>
    </div>
  );
};
