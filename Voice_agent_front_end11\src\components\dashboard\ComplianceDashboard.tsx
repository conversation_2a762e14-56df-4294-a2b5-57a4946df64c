import React from 'react';
import { VoiceAnalytics } from '../../types/voice/VoiceTypes';

interface ComplianceDashboardProps {
  sessionId: string;
  analytics: VoiceAnalytics;
}

export const ComplianceDashboard: React.FC<ComplianceDashboardProps> = ({ sessionId, analytics }) => {
  return (
    <div className="compliance-dashboard bg-white/10 p-4 rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Compliance & Analytics</h3>
      <div className="text-xs text-gray-300 mb-2">Session: {sessionId}</div>
      <div className="mb-2">
        <strong>Metrics:</strong>
        <pre className="bg-black/20 p-2 rounded text-xs text-white overflow-x-auto">
          {JSON.stringify(analytics.metrics, null, 2)}
        </pre>
      </div>
      <div className="mb-2">
        <strong>Quality Metrics:</strong>
        <pre className="bg-black/20 p-2 rounded text-xs text-white overflow-x-auto">
          {JSON.stringify(analytics.qualityMetrics, null, 2)}
        </pre>
      </div>
      <div>
        <strong>Compliance Events:</strong>
        <pre className="bg-black/20 p-2 rounded text-xs text-white overflow-x-auto">
          {JSON.stringify(analytics.complianceEvents, null, 2)}
        </pre>
      </div>
    </div>
  );
}; 