[ 2025-08-07 12:23:58,623 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 12:23:58,623 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 12:23:59,032 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4086 seconds.
[ 2025-08-07 12:24:03,000 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 12:24:11,509 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:24:11,509 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:24:11,510 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 12:24:12,229 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:24:12,230 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:24:12,231 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 12:24:12,231 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 12:24:12,238 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 12:24:12,309 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,310 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,310 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,311 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,311 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,312 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,312 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,312 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,312 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,313 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,313 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,315 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:12,512 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,512 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,513 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,515 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,515 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,515 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:12,515 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:13,105 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 12:24:19,617 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:24:19,820 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:24:19,822 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:24:20,893 ] 666 root - INFO - Call logging started for call ID: SCL_u6MyYeK353ss
[ 2025-08-07 12:24:25,982 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:24:25,987 ] 755 root - INFO - Call logging ended for call ID: SCL_u6MyYeK353ss
[ 2025-08-07 12:24:25,988 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:24:33,694 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 12:24:33,695 ] 207 root - INFO - \u26a1 Language detection: 0.198s
[ 2025-08-07 12:24:33,695 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 12:24:33,695 ] 513 root - INFO - Enhanced web search query: Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:24:33,695 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:24:33,696 ] 45 root - INFO - \U0001f310 Fast web search: 'Chief Minister of Andhra Prade...'
[ 2025-08-07 12:24:36,418 ] 59 root - INFO - \u26a1 Web search: 2.72s, 2 results
[ 2025-08-07 12:24:36,419 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7239 seconds.
[ 2025-08-07 12:24:36,419 ] 525 root - INFO - \U0001f310 Web search: 2.92s, 2 results
[ 2025-08-07 12:24:37,033 ] 513 root - INFO - Enhanced web search query: current Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:24:37,035 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:24:37,035 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of Andh...'
[ 2025-08-07 12:24:39,904 ] 59 root - INFO - \u26a1 Web search: 2.87s, 2 results
[ 2025-08-07 12:24:39,904 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8689 seconds.
[ 2025-08-07 12:24:39,905 ] 525 root - INFO - \U0001f310 Web search: 2.87s, 2 results
[ 2025-08-07 12:24:40,968 ] 513 root - INFO - Enhanced web search query: current Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:24:40,970 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:24:40,972 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of Andh...'
[ 2025-08-07 12:24:43,667 ] 59 root - INFO - \u26a1 Web search: 2.70s, 2 results
[ 2025-08-07 12:24:43,667 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6972 seconds.
[ 2025-08-07 12:24:43,668 ] 525 root - INFO - \U0001f310 Web search: 2.70s, 2 results
[ 2025-08-07 12:24:45,640 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:24:48,138 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:24:50,290 ] 513 root - INFO - Enhanced web search query: current president of USA as of 2025 as of 2025
[ 2025-08-07 12:24:50,291 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:24:50,292 ] 45 root - INFO - \U0001f310 Fast web search: 'current president of USA as of...'
[ 2025-08-07 12:24:52,347 ] 59 root - INFO - \u26a1 Web search: 2.05s, 2 results
[ 2025-08-07 12:24:52,347 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.0560 seconds.
[ 2025-08-07 12:24:52,348 ] 525 root - INFO - \U0001f310 Web search: 2.06s, 2 results
[ 2025-08-07 12:25:09,650 ] 513 root - INFO - Enhanced web search query: current temperature in Hyderabad as of 2025 as of 2025
[ 2025-08-07 12:25:09,653 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:09,654 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in Hyderab...'
[ 2025-08-07 12:25:12,127 ] 59 root - INFO - \u26a1 Web search: 2.47s, 2 results
[ 2025-08-07 12:25:12,128 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4753 seconds.
[ 2025-08-07 12:25:12,129 ] 525 root - INFO - \U0001f310 Web search: 2.48s, 2 results
[ 2025-08-07 12:25:12,494 ] 513 root - INFO - Enhanced web search query: current temperature in Hyderabad as of 2025 as of 2025
[ 2025-08-07 12:25:12,497 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:12,498 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in Hyderab...'
[ 2025-08-07 12:25:15,725 ] 59 root - INFO - \u26a1 Web search: 3.23s, 2 results
[ 2025-08-07 12:25:15,726 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.2292 seconds.
[ 2025-08-07 12:25:15,726 ] 525 root - INFO - \U0001f310 Web search: 3.23s, 2 results
[ 2025-08-07 12:25:16,458 ] 513 root - INFO - Enhanced web search query: current temperature in Hyderabad as of 2025 as of 2025
[ 2025-08-07 12:25:16,460 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:16,461 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in Hyderab...'
[ 2025-08-07 12:25:21,002 ] 59 root - INFO - \u26a1 Web search: 4.54s, 2 results
[ 2025-08-07 12:25:21,004 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.5441 seconds.
[ 2025-08-07 12:25:21,005 ] 525 root - INFO - \U0001f310 Web search: 4.55s, 2 results
[ 2025-08-07 12:25:46,161 ] 513 root - INFO - Enhanced web search query: current temperature in Hyderabad as of 2025 as of 2025
[ 2025-08-07 12:25:46,162 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:46,165 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in Hyderab...'
[ 2025-08-07 12:25:49,069 ] 59 root - INFO - \u26a1 Web search: 2.90s, 2 results
[ 2025-08-07 12:25:49,070 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9085 seconds.
[ 2025-08-07 12:25:49,075 ] 525 root - INFO - \U0001f310 Web search: 2.91s, 2 results
[ 2025-08-07 12:25:51,078 ] 513 root - INFO - Enhanced web search query: current temperature in India as of 2025 as of 2025
[ 2025-08-07 12:25:51,080 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:51,083 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in India a...'
[ 2025-08-07 12:25:53,578 ] 59 root - INFO - \u26a1 Web search: 2.49s, 2 results
[ 2025-08-07 12:25:53,580 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4995 seconds.
[ 2025-08-07 12:25:53,583 ] 525 root - INFO - \U0001f310 Web search: 2.51s, 2 results
[ 2025-08-07 12:25:54,245 ] 513 root - INFO - Enhanced web search query: current temperature in India as of 2025 as of 2025
[ 2025-08-07 12:25:54,252 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:54,258 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in India a...'
[ 2025-08-07 12:25:56,685 ] 59 root - INFO - \u26a1 Web search: 2.43s, 2 results
[ 2025-08-07 12:25:56,689 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4371 seconds.
[ 2025-08-07 12:25:56,692 ] 525 root - INFO - \U0001f310 Web search: 2.45s, 2 results
[ 2025-08-07 12:25:57,716 ] 513 root - INFO - Enhanced web search query: current temperature in India as of 2025 as of 2025
[ 2025-08-07 12:25:57,720 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:25:57,725 ] 45 root - INFO - \U0001f310 Fast web search: 'current temperature in India a...'
[ 2025-08-07 12:26:00,045 ] 59 root - INFO - \u26a1 Web search: 2.32s, 2 results
[ 2025-08-07 12:26:00,046 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3262 seconds.
[ 2025-08-07 12:26:00,048 ] 525 root - INFO - \U0001f310 Web search: 2.33s, 2 results
[ 2025-08-07 12:28:10,311 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:28:10,313 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:10,314 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:28:12,730 ] 59 root - INFO - \u26a1 Web search: 2.42s, 2 results
[ 2025-08-07 12:28:12,732 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4199 seconds.
[ 2025-08-07 12:28:12,735 ] 525 root - INFO - \U0001f310 Web search: 2.42s, 2 results
[ 2025-08-07 12:28:13,946 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:28:13,948 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:13,952 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:28:16,172 ] 59 root - INFO - \u26a1 Web search: 2.22s, 2 results
[ 2025-08-07 12:28:16,173 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2245 seconds.
[ 2025-08-07 12:28:16,177 ] 525 root - INFO - \U0001f310 Web search: 2.23s, 2 results
[ 2025-08-07 12:28:16,929 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:28:16,930 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:16,931 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:28:19,489 ] 59 root - INFO - \u26a1 Web search: 2.56s, 2 results
[ 2025-08-07 12:28:19,490 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5597 seconds.
[ 2025-08-07 12:28:19,491 ] 525 root - INFO - \U0001f310 Web search: 2.56s, 2 results
[ 2025-08-07 12:28:34,349 ] 513 root - INFO - Enhanced web search query: current Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:28:34,353 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:34,353 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of Andh...'
[ 2025-08-07 12:28:36,923 ] 59 root - INFO - \u26a1 Web search: 2.57s, 2 results
[ 2025-08-07 12:28:36,924 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5722 seconds.
[ 2025-08-07 12:28:36,925 ] 525 root - INFO - \U0001f310 Web search: 2.58s, 2 results
[ 2025-08-07 12:28:37,673 ] 513 root - INFO - Enhanced web search query: current Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:28:37,674 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:37,675 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of Andh...'
[ 2025-08-07 12:28:40,371 ] 59 root - INFO - \u26a1 Web search: 2.69s, 2 results
[ 2025-08-07 12:28:40,371 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6968 seconds.
[ 2025-08-07 12:28:40,376 ] 525 root - INFO - \U0001f310 Web search: 2.70s, 2 results
[ 2025-08-07 12:28:41,367 ] 513 root - INFO - Enhanced web search query: current Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-07 12:28:41,368 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:28:41,369 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of Andh...'
[ 2025-08-07 12:28:43,430 ] 59 root - INFO - \u26a1 Web search: 2.06s, 2 results
[ 2025-08-07 12:28:43,431 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.0627 seconds.
[ 2025-08-07 12:28:43,432 ] 525 root - INFO - \U0001f310 Web search: 2.07s, 2 results
[ 2025-08-07 12:28:52,047 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:29:12,216 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:29:35,482 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:29:35,953 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:29:35,964 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:29:37,525 ] 666 root - INFO - Call logging started for call ID: SCL_9v4gzUsh2WAA
[ 2025-08-07 12:29:42,783 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:29:42,794 ] 755 root - INFO - Call logging ended for call ID: SCL_9v4gzUsh2WAA
[ 2025-08-07 12:29:42,795 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:29:51,087 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 12:29:51,090 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 12:29:51,090 ] 513 root - INFO - Enhanced web search query: Bay House Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:29:51,092 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:29:51,093 ] 45 root - INFO - \U0001f310 Fast web search: 'Bay House Chief Minister as of...'
[ 2025-08-07 12:29:53,855 ] 59 root - INFO - \u26a1 Web search: 2.76s, 2 results
[ 2025-08-07 12:29:53,856 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7638 seconds.
[ 2025-08-07 12:29:53,857 ] 525 root - INFO - \U0001f310 Web search: 2.77s, 2 results
[ 2025-08-07 12:29:54,508 ] 513 root - INFO - Enhanced web search query: Bay House Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:29:54,509 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:29:54,511 ] 45 root - INFO - \U0001f310 Fast web search: 'Bay House Chief Minister as of...'
[ 2025-08-07 12:29:56,913 ] 59 root - INFO - \u26a1 Web search: 2.40s, 2 results
[ 2025-08-07 12:29:56,919 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4096 seconds.
[ 2025-08-07 12:29:56,922 ] 525 root - INFO - \U0001f310 Web search: 2.41s, 2 results
[ 2025-08-07 12:30:06,743 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-08-07 12:30:11,215 ] 513 root - INFO - Enhanced web search query: Delhi Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:30:11,216 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:30:11,222 ] 45 root - INFO - \U0001f310 Fast web search: 'Delhi Chief Minister as of 202...'
[ 2025-08-07 12:30:15,603 ] 59 root - INFO - \u26a1 Web search: 4.38s, 2 results
[ 2025-08-07 12:30:15,605 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.3883 seconds.
[ 2025-08-07 12:30:15,607 ] 525 root - INFO - \U0001f310 Web search: 4.39s, 2 results
[ 2025-08-07 12:30:29,617 ] 513 root - INFO - Enhanced web search query: Delhi Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:30:29,617 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:30:29,617 ] 45 root - INFO - \U0001f310 Fast web search: 'Delhi Chief Minister as of 202...'
[ 2025-08-07 12:30:30,849 ] 513 root - INFO - Enhanced web search query: Goa Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:30:30,852 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:30:30,853 ] 45 root - INFO - \U0001f310 Fast web search: 'Goa Chief Minister as of 2025 ...'
[ 2025-08-07 12:30:31,967 ] 59 root - INFO - \u26a1 Web search: 2.35s, 2 results
[ 2025-08-07 12:30:31,969 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3520 seconds.
[ 2025-08-07 12:30:31,970 ] 525 root - INFO - \U0001f310 Web search: 2.35s, 2 results
[ 2025-08-07 12:30:33,373 ] 59 root - INFO - \u26a1 Web search: 2.52s, 2 results
[ 2025-08-07 12:30:33,374 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5217 seconds.
[ 2025-08-07 12:30:33,375 ] 525 root - INFO - \U0001f310 Web search: 2.53s, 2 results
[ 2025-08-07 12:30:46,699 ] 513 root - INFO - Enhanced web search query: Goa beaches as of 2025 as of 2025
[ 2025-08-07 12:30:46,703 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:30:46,705 ] 45 root - INFO - \U0001f310 Fast web search: 'Goa beaches as of 2025 as of 2...'
[ 2025-08-07 12:30:48,859 ] 59 root - INFO - \u26a1 Web search: 2.15s, 2 results
[ 2025-08-07 12:30:48,859 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.1556 seconds.
[ 2025-08-07 12:30:48,860 ] 525 root - INFO - \U0001f310 Web search: 2.16s, 2 results
[ 2025-08-07 12:30:52,779 ] 513 root - INFO - Enhanced web search query: best beaches in Goa as of 2025 as of 2025
[ 2025-08-07 12:30:52,780 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:30:52,781 ] 45 root - INFO - \U0001f310 Fast web search: 'best beaches in Goa as of 2025...'
[ 2025-08-07 12:30:55,855 ] 59 root - INFO - \u26a1 Web search: 3.07s, 2 results
[ 2025-08-07 12:30:55,856 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.0763 seconds.
[ 2025-08-07 12:30:55,857 ] 525 root - INFO - \U0001f310 Web search: 3.08s, 2 results
[ 2025-08-07 12:31:22,510 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:31:22,510 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear operational voltages...'
[ 2025-08-07 12:31:25,680 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:31:25,681 ] 159 root - INFO - \u26a1 New response generated in 3.17s
[ 2025-08-07 12:31:25,682 ] 51 root - INFO - Vector DB search completed in 3.17s
[ 2025-08-07 12:31:25,682 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:31:25,683 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.1732 seconds.
[ 2025-08-07 12:31:25,685 ] 457 root - INFO - \u2705 Vector search: 3.18s
[ 2025-08-07 12:31:49,630 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:31:49,631 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear transformer operational voltages...'
[ 2025-08-07 12:31:51,157 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:31:51,157 ] 159 root - INFO - \u26a1 New response generated in 1.52s
[ 2025-08-07 12:31:51,158 ] 51 root - INFO - Vector DB search completed in 1.53s
[ 2025-08-07 12:31:51,158 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:31:51,158 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.5279 seconds.
[ 2025-08-07 12:31:51,160 ] 457 root - INFO - \u2705 Vector search: 1.53s
[ 2025-08-07 12:31:53,662 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:31:53,663 ] 37 root - INFO - \U0001f50d Vector DB search for: 'NKT cables and switches specifications...'
[ 2025-08-07 12:31:55,134 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:31:55,137 ] 159 root - INFO - \u26a1 New response generated in 1.47s
[ 2025-08-07 12:31:55,137 ] 51 root - INFO - Vector DB search completed in 1.47s
[ 2025-08-07 12:31:55,138 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:31:55,139 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.4770 seconds.
[ 2025-08-07 12:31:55,141 ] 457 root - INFO - \u2705 Vector search: 1.48s
[ 2025-08-07 12:32:25,862 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:32:25,865 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB transformers specifications...'
[ 2025-08-07 12:32:26,987 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:32:26,988 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG architecture...'
[ 2025-08-07 12:32:28,056 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:32:28,056 ] 159 root - INFO - \u26a1 New response generated in 2.19s
[ 2025-08-07 12:32:28,056 ] 51 root - INFO - Vector DB search completed in 2.19s
[ 2025-08-07 12:32:28,057 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:32:28,057 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.1953 seconds.
[ 2025-08-07 12:32:28,058 ] 457 root - INFO - \u2705 Vector search: 2.20s
[ 2025-08-07 12:32:28,737 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:32:28,742 ] 159 root - INFO - \u26a1 New response generated in 1.75s
[ 2025-08-07 12:32:28,752 ] 51 root - INFO - Vector DB search completed in 1.76s
[ 2025-08-07 12:32:28,760 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:32:28,762 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.7753 seconds.
[ 2025-08-07 12:32:28,809 ] 457 root - INFO - \u2705 Vector search: 1.82s
[ 2025-08-07 12:32:44,033 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-08-07 12:32:50,095 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:32:50,097 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG architecture...'
[ 2025-08-07 12:32:50,098 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 12:32:50,099 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 12:32:50,100 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:32:50,100 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0051 seconds.
[ 2025-08-07 12:32:50,102 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 12:32:56,256 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:32:56,257 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG architecture...'
[ 2025-08-07 12:32:56,259 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 12:32:56,260 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 12:32:56,260 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:32:56,261 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0050 seconds.
[ 2025-08-07 12:32:56,261 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-07 12:32:57,556 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:32:57,557 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG architecture components...'
[ 2025-08-07 12:32:58,957 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:32:58,958 ] 159 root - INFO - \u26a1 New response generated in 1.40s
[ 2025-08-07 12:32:58,958 ] 51 root - INFO - Vector DB search completed in 1.40s
[ 2025-08-07 12:32:58,959 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:32:58,959 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.4030 seconds.
[ 2025-08-07 12:32:58,960 ] 457 root - INFO - \u2705 Vector search: 1.41s
[ 2025-08-07 12:33:16,008 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:33:39,228 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:54:43,986 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:54:46,500 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 13:09:44,816 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 13:09:47,342 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 13:24:42,797 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 13:24:47,789 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 13:24:54,972 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 13:24:54,976 ] 560 livekit.agents - INFO - shutting down worker
