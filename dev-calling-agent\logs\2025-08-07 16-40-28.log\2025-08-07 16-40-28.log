[ 2025-08-07 16:40:33,483 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 16:40:33,483 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 16:40:33,916 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4338 seconds.
[ 2025-08-07 16:40:39,783 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 16:40:49,068 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:40:49,068 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:40:49,068 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 16:40:49,969 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:40:49,970 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:40:49,971 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 16:40:49,971 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 16:40:49,975 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 16:40:50,019 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,023 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,025 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,028 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,030 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,032 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,033 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,039 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,042 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,042 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,043 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,043 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:40:50,205 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,205 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,206 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,206 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,207 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,207 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,207 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,207 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,207 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,208 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,208 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,208 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:40:50,703 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 16:41:17,920 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:41:18,129 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:41:18,132 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:41:19,617 ] 666 root - INFO - Call logging started for call ID: SCL_Q22LHmAX5oud
[ 2025-08-07 16:41:23,275 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:41:26,526 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:41:27,162 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:41:29,605 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:41:30,156 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:41:30,195 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:41:30,210 ] 755 root - INFO - Call logging ended for call ID: SCL_Q22LHmAX5oud
[ 2025-08-07 16:41:30,211 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:41:53,612 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:42:51,160 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:42:51,472 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:42:51,473 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:42:51,679 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:42:51,694 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:42:51,728 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:42:51,732 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:42:53,662 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:42:56,700 ] 666 root - INFO - Call logging started for call ID: SCL_Rtr26K98p6rn
[ 2025-08-07 16:42:59,361 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 2.67 seconds
[ 2025-08-07 16:43:09,666 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:43:09,728 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:43:09,744 ] 755 root - INFO - Call logging ended for call ID: SCL_Rtr26K98p6rn
[ 2025-08-07 16:43:09,745 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:43:16,289 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:43:18,390 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:43:21,318 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:43:28,993 ] 252 livekit.agents - WARNING - The room connection was not established within 10 seconds after calling job_entry. This may indicate that job_ctx.connect() was not called. 
[ 2025-08-07 16:43:30,458 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:43:33,655 ] 666 root - INFO - Call logging started for call ID: SCL_iMP7sp9gKPGA
[ 2025-08-07 16:43:34,233 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:43:36,755 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:43:36,933 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 3.30 seconds
[ 2025-08-07 16:43:49,319 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:43:50,140 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 689, in _run
    raise APITimeoutError(retryable=retryable) from None
livekit.agents._exceptions.APITimeoutError: Request timed out. (body=None, retryable=True)
[ 2025-08-07 16:43:52,439 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:43:54,318 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:44:00,692 ] 666 root - INFO - Call logging started for call ID: SCL_aucYER2b3qo8
[ 2025-08-07 16:44:02,588 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:44:02,646 ] 755 root - INFO - Call logging ended for call ID: SCL_aucYER2b3qo8
[ 2025-08-07 16:44:02,681 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:44:04,145 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 3.47 seconds
[ 2025-08-07 16:44:10,968 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:44:11,910 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:44:14,406 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:44:14,433 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:44:20,010 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:44:26,057 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:44:26,467 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:44:26,503 ] 755 root - INFO - Call logging ended for call ID: SCL_aucYER2b3qo8
[ 2025-08-07 16:44:26,508 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:44:31,605 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:44:39,505 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:44:42,030 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:44:47,051 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:44:47,202 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:45:02,111 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:45:02,560 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "server request to leave" StateMismatch Reconnect
[ 2025-08-07 16:45:02,561 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:45:02,564 ] 167 livekit - ERROR - livekit::rtc_engine:729:livekit::rtc_engine - resuming connection... attempt: 0
[ 2025-08-07 16:45:05,572 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:45:07,130 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:45:08,491 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:45:12,175 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:45:13,070 ] 836 livekit.agents - WARNING - assignment for job AJ_eQuUA7vMLTSw timed out
[ 2025-08-07 16:45:13,073 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 16:45:16,017 ] 836 livekit.agents - WARNING - assignment for job AJ_eQuUA7vMLTSw timed out
[ 2025-08-07 16:45:16,019 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 16:45:18,690 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:45:25,383 ] 167 livekit - ERROR - livekit::rtc_engine:731:livekit::rtc_engine - resuming connection failed: signal failure: ws failure: Connection closed normally
[ 2025-08-07 16:45:25,386 ] 167 livekit - ERROR - livekit::rtc_engine:663:livekit::rtc_engine - failed to reconnect to the livekit room
[ 2025-08-07 16:45:26,204 ] 836 livekit.agents - WARNING - assignment for job AJ_eQuUA7vMLTSw timed out
[ 2025-08-07 16:45:26,209 ] 871 livekit.agents - ERROR - job_request_fnc failed
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 869, in _job_request_task
    await self._opts.request_fnc(job_req)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 68, in _default_request_fnc
    await ctx.accept()
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\job.py", line 548, in accept
    await self._on_accept(accept_arguments)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 840, in _on_accept
    raise AssignmentTimeoutError() from None
livekit.agents._exceptions.AssignmentTimeoutError
[ 2025-08-07 16:45:35,856 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 16:45:36,752 ] 107 livekit.agents - WARNING - exiting forcefully
