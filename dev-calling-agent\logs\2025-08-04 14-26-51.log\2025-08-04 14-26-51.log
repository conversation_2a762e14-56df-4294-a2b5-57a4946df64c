[ 2025-08-04 14:26:55,931 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 14:26:55,932 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 14:26:56,440 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5078 seconds.
[ 2025-08-04 14:27:01,918 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-04 14:27:11,514 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 14:27:11,514 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 14:27:11,514 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 14:28:22,661 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 14:28:22,662 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 14:28:22,662 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 14:28:22,663 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 14:28:22,670 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 14:28:22,676 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 14:28:22,689 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 14:28:22,694 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 14:28:22,696 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 14:28:23,592 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 14:28:23,932 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3405 seconds.
[ 2025-08-04 14:28:23,932 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 14:28:23,932 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 14:28:23,933 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 14:29:35,423 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<_GatheringFuture pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 72.59 seconds
[ 2025-08-04 14:29:36,210 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 14:29:36,213 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 14:29:36,214 ] 21 livekit.agents - ERROR - Error in _pipeline_reply_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:29:36,385 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 14:29:36,392 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 14:29:41,807 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 14:29:41,811 ] 21 livekit.agents - ERROR - Error in _pipeline_reply_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:29:42,728 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 14:29:42,733 ] 21 livekit.agents - ERROR - Error in _pipeline_reply_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:29:51,445 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 14:29:51,447 ] 21 livekit.agents - ERROR - Error in _pipeline_reply_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:30:01,323 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-08-04 14:30:01,327 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-08-04 14:30:01,340 ] 1785 asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.pipeline_reply' coro=<AgentActivity._pipeline_reply_task() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=ValueError("unknown tool type: <class 'src.agent.agent.AgenticRAG'>")>
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:30:01,341 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-08-04 14:30:01,342 ] 1785 asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.pipeline_reply' coro=<AgentActivity._pipeline_reply_task() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=ValueError("unknown tool type: <class 'src.agent.agent.AgenticRAG'>")>
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:30:01,348 ] 650 livekit.agents - DEBUG - session closed
[ 2025-08-04 14:30:01,349 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-08-04 14:30:01,351 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
[ 2025-08-04 14:30:01,663 ] 1785 asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.pipeline_reply' coro=<AgentActivity._pipeline_reply_task() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=ValueError("unknown tool type: <class 'src.agent.agent.AgenticRAG'>")>
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
[ 2025-08-04 14:30:01,664 ] 1785 asyncio - ERROR - Task exception was never retrieved
future: <Task finished name='AgentActivity.pipeline_reply' coro=<AgentActivity._pipeline_reply_task() done, defined at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:68> exception=ValueError("unknown tool type: <class 'src.agent.agent.AgenticRAG'>")>
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent_activity.py", line 1433, in _pipeline_reply_task
    tool_ctx = llm.ToolContext(tools)
               ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 219, in __init__
    self.update_tools(tools)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\tool_context.py", line 244, in update_tools
    raise ValueError(f"unknown tool type: {type(tool)}")
ValueError: unknown tool type: <class 'src.agent.agent.AgenticRAG'>
