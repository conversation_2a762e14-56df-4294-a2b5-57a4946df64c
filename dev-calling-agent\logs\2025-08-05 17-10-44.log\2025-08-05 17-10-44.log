[ 2025-08-05 17:10:51,295 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-05 17:10:51,297 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-05 17:10:51,860 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5636 seconds.
[ 2025-08-05 17:11:00,497 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-05 17:11:12,044 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 17:11:12,045 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 17:11:12,045 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-05 17:11:13,020 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 17:11:13,020 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 17:11:13,020 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-05 17:11:13,020 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-05 17:11:13,027 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-05 17:11:13,087 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,088 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,090 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,093 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,095 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,096 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,097 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,098 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,099 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,100 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,100 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,101 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 17:11:13,137 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,140 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,143 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,144 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,145 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,146 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,146 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,147 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,147 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,149 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,150 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,150 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 17:11:13,839 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-05 17:12:36,662 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-05 17:12:39,178 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-05 17:14:01,891 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-05 17:14:04,400 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-05 17:15:42,763 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-05 17:15:42,765 ] 560 livekit.agents - INFO - shutting down worker
