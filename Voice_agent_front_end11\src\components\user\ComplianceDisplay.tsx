
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, Lock, Eye, FileText, Globe, Server } from 'lucide-react';

export const ComplianceDisplay = () => {
  const complianceItems = [
    {
      name: 'GDPR',
      description: 'General Data Protection Regulation',
      status: 'Compliant',
      icon: <Shield className="w-4 h-4" />,
      details: 'Your data is processed lawfully with your consent'
    },
    {
      name: 'ISO 27001',
      description: 'Information Security Management',
      status: 'Certified',
      icon: <Lock className="w-4 h-4" />,
      details: 'Enterprise-grade security controls implemented'
    },
    {
      name: 'EU AI Act',
      description: 'European Union AI Regulation',
      status: 'Ready',
      icon: <Eye className="w-4 h-4" />,
      details: 'AI transparency and explainability measures in place'
    },
    {
      name: 'DORA',
      description: 'Digital Operational Resilience Act',
      status: 'Compliant',
      icon: <Server className="w-4 h-4" />,
      details: 'Operational resilience and risk management'
    },
    {
      name: 'CCPA',
      description: 'California Consumer Privacy Act',
      status: 'Compliant',
      icon: <FileText className="w-4 h-4" />,
      details: 'Privacy rights for California residents'
    },
    {
      name: 'SOC 2',
      description: 'Service Organization Control 2',
      status: 'Type II',
      icon: <Globe className="w-4 h-4" />,
      details: 'Third-party audited security controls'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'compliant':
      case 'certified':
        return 'bg-green-600';
      case 'ready':
      case 'type ii':
        return 'bg-blue-600';
      default:
        return 'bg-gray-600';
    }
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Shield className="w-5 h-5 mr-2" />
        Data Protection & Compliance
      </h3>
      
      <div className="grid md:grid-cols-2 gap-4">
        {complianceItems.map((item) => (
          <div key={item.name} className="p-4 bg-white/10 rounded-lg border border-white/20">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                <div className="text-white">{item.icon}</div>
                <span className="text-white font-medium">{item.name}</span>
              </div>
              <Badge className={`${getStatusColor(item.status)} text-white`}>
                {item.status}
              </Badge>
            </div>
            <p className="text-white/70 text-sm mb-1">{item.description}</p>
            <p className="text-white/60 text-xs">{item.details}</p>
          </div>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-500/20 rounded-lg border border-blue-400/30">
        <h4 className="text-blue-200 font-medium mb-2">Your Data Rights</h4>
        <ul className="text-blue-100 text-sm space-y-1">
          <li>• Right to access your personal data</li>
          <li>• Right to rectification and erasure</li>
          <li>• Right to data portability</li>
          <li>• Right to object to processing</li>
          <li>• Right to withdraw consent at any time</li>
        </ul>
      </div>
    </Card>
  );
};
