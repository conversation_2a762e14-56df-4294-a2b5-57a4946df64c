from langgraphagent import entrypoint
from fastapi import FastAPI
import subprocess

app = FastAPI()

# Store the process reference
agent_process = None
@app.get("/")
def home():
    return{"mesage":"server runnning"}


@app.get("/start-agent")
def start_langgraph_agent():
    global agent_process
    if agent_process is None or agent_process.poll() is not None:
        agent_process = subprocess.Popen(
            ["python", "langgraphagent.py", "start"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        return {"status": "started", "detail": "LangGraph agent started"}
    else:
        return {"status": "already running"}

@app.get("/stop-agent")
def stop_langgraph_agent():
    global agent_process
    if agent_process and agent_process.poll() is None:
        agent_process.terminate()
        return {"status": "stopped", "detail": "LangGraph agent stopped"}
    return {"status": "not running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)