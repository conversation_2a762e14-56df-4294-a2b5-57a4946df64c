// AnalyticsService: Handles real-time and historical analytics
// TODO: Integrate with backend for persistent analytics

export type VoiceAnalytics = {
  sessionId: string;
  metrics: {
    startTime: string;
    endTime?: string;
    duration?: number;
    audioDuration?: number;
    processingTime?: number;
    confidence?: number;
    errors?: number;
    latency?: number;
    recognitionAccuracy?: number;
  };
  qualityMetrics: {
    audioQuality?: number;
    recognitionAccuracy?: number;
    latency?: number;
  };
  complianceEvents: any[];
};

export class AnalyticsService {
  private analytics: VoiceAnalytics[] = [];

  logAnalytics(analytics: VoiceAnalytics) {
    this.analytics.push(analytics);
  }

  getAnalytics(): VoiceAnalytics[] {
    return this.analytics;
  }

  exportAnalytics(): string {
    return JSON.stringify(this.analytics, null, 2);
  }
} 