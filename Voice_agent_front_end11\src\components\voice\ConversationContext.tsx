
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  User, 
  Clock, 
  Tag, 
  Trash2,
  Database as DatabaseIcon,
  TrendingUp
} from 'lucide-react';

interface ConversationContext {
  userId: string;
  sessionId: string;
  customerProfile: {
    name?: string;
    preferredLanguage: string;
    industry: string;
    accountType: string;
    interactionHistory: InteractionSummary[];
    preferences: { [key: string]: any };
    sentimentTrend: number[];
  };
  currentSession: {
    startTime: Date;
    intents: string[];
    entities: { [key: string]: string };
    topics: string[];
    escalationFlags: string[];
    resolutionStatus: 'pending' | 'resolved' | 'escalated';
  };
  longTermMemory: {
    previousIssues: string[];
    resolutionPreferences: string[];
    communicationStyle: 'formal' | 'casual' | 'technical';
    satisfactionHistory: number[];
  };
}

interface InteractionSummary {
  date: Date;
  duration: number;
  intent: string;
  resolution: string;
  satisfaction: number;
}

interface ConversationContextProps {
  userId: string;
  onContextUpdate: (context: ConversationContext) => void;
}

export const ConversationContext = ({ 
  userId, 
  onContextUpdate 
}: ConversationContextProps) => {
  const [context, setContext] = useState<ConversationContext | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize or load context
  useEffect(() => {
    loadConversationContext(userId);
  }, [userId]);

  const loadConversationContext = async (userId: string) => {
    setIsLoading(true);
    
    try {
      // In production, this would load from your backend/database
      const storedContext = localStorage.getItem(`context_${userId}`);
      
      if (storedContext) {
        const parsedContext = JSON.parse(storedContext);
        setContext({
          ...parsedContext,
          currentSession: {
            ...parsedContext.currentSession,
            startTime: new Date(),
            resolutionStatus: 'pending'
          }
        });
      } else {
        // Create new context for first-time user
        const newContext: ConversationContext = {
          userId,
          sessionId: generateSessionId(),
          customerProfile: {
            preferredLanguage: 'en',
            industry: 'general',
            accountType: 'standard',
            interactionHistory: [],
            preferences: {},
            sentimentTrend: [0]
          },
          currentSession: {
            startTime: new Date(),
            intents: [],
            entities: {},
            topics: [],
            escalationFlags: [],
            resolutionStatus: 'pending'
          },
          longTermMemory: {
            previousIssues: [],
            resolutionPreferences: [],
            communicationStyle: 'casual',
            satisfactionHistory: []
          }
        };
        
        setContext(newContext);
        saveContext(newContext);
      }
    } catch (error) {
      console.error('Error loading conversation context:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateSessionId = () => {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const saveContext = (contextData: ConversationContext) => {
    localStorage.setItem(`context_${userId}`, JSON.stringify(contextData));
    onContextUpdate(contextData);
  };

  const updateContext = (updates: Partial<ConversationContext>) => {
    if (!context) return;
    
    const updatedContext = {
      ...context,
      ...updates,
      currentSession: {
        ...context.currentSession,
        ...updates.currentSession
      }
    };
    
    setContext(updatedContext);
    saveContext(updatedContext);
  };

  const addIntent = (intent: string) => {
    if (!context) return;
    
    const updatedIntents = [...context.currentSession.intents, intent];
    updateContext({
      currentSession: {
        ...context.currentSession,
        intents: updatedIntents
      }
    });
  };

  const addEntity = (key: string, value: string) => {
    if (!context) return;
    
    updateContext({
      currentSession: {
        ...context.currentSession,
        entities: {
          ...context.currentSession.entities,
          [key]: value
        }
      }
    });
  };

  const addEscalationFlag = (flag: string) => {
    if (!context) return;
    
    const updatedFlags = [...context.currentSession.escalationFlags, flag];
    updateContext({
      currentSession: {
        ...context.currentSession,
        escalationFlags: updatedFlags
      }
    });
  };

  const updateSentiment = (sentiment: number) => {
    if (!context) return;
    
    const updatedTrend = [...context.customerProfile.sentimentTrend.slice(-9), sentiment];
    updateContext({
      customerProfile: {
        ...context.customerProfile,
        sentimentTrend: updatedTrend
      }
    });
  };

  const endSession = (resolution: string, satisfaction: number) => {
    if (!context) return;
    
    const sessionDuration = Date.now() - context.currentSession.startTime.getTime();
    const interactionSummary: InteractionSummary = {
      date: new Date(),
      duration: sessionDuration,
      intent: context.currentSession.intents[0] || 'general_inquiry',
      resolution,
      satisfaction
    };

    updateContext({
      customerProfile: {
        ...context.customerProfile,
        interactionHistory: [...context.customerProfile.interactionHistory, interactionSummary]
      },
      longTermMemory: {
        ...context.longTermMemory,
        satisfactionHistory: [...context.longTermMemory.satisfactionHistory, satisfaction]
      },
      currentSession: {
        ...context.currentSession,
        resolutionStatus: 'resolved'
      }
    });
  };

  const clearContext = () => {
    localStorage.removeItem(`context_${userId}`);
    setContext(null);
    loadConversationContext(userId);
  };

  const getAverageSatisfaction = () => {
    if (!context || context.longTermMemory.satisfactionHistory.length === 0) return 0;
    const sum = context.longTermMemory.satisfactionHistory.reduce((a, b) => a + b, 0);
    return Math.round(sum / context.longTermMemory.satisfactionHistory.length);
  };

  const getSessionDuration = () => {
    if (!context) return 0;
    return Math.round((Date.now() - context.currentSession.startTime.getTime()) / 1000 / 60);
  };

  if (isLoading) {
    return (
      <Card className="p-4 bg-gray-900/50 border-gray-700">
        <div className="flex items-center space-x-2">
          <Database className="w-5 h-5 text-blue-400 animate-pulse" />
          <span className="text-white">Loading conversation context...</span>
        </div>
      </Card>
    );
  }

  if (!context) return null;

  return (
    <Card className="p-4 bg-gray-900/50 border-gray-700">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Database className="w-5 h-5 text-blue-400" />
          <h3 className="text-white font-semibold">Conversation Context</h3>
        </div>
        <Button
          onClick={clearContext}
          variant="outline"
          size="sm"
          className="text-red-400 border-red-400 hover:bg-red-400/10"
        >
          <Trash2 className="w-3 h-3 mr-1" />
          Clear
        </Button>
      </div>

      <div className="space-y-4">
        {/* Customer Profile */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <User className="w-4 h-4 text-green-400" />
              <span className="text-green-400 text-sm font-medium">Customer Profile</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Industry:</span>
                <Badge variant="outline" className="text-xs">
                  {context.customerProfile.industry}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Account:</span>
                <span className="text-white capitalize">{context.customerProfile.accountType}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Language:</span>
                <span className="text-white uppercase">{context.customerProfile.preferredLanguage}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Avg Satisfaction:</span>
                <span className="text-white">{getAverageSatisfaction()}%</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-purple-400" />
              <span className="text-purple-400 text-sm font-medium">Current Session</span>
            </div>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Duration:</span>
                <span className="text-white">{getSessionDuration()}m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Intents:</span>
                <span className="text-white">{context.currentSession.intents.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <Badge 
                  variant={context.currentSession.resolutionStatus === 'resolved' ? 'default' : 'outline'}
                  className="text-xs"
                >
                  {context.currentSession.resolutionStatus}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Current Session Data */}
        {context.currentSession.intents.length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <Tag className="w-4 h-4 text-yellow-400" />
              <span className="text-yellow-400 text-sm font-medium">Session Intents</span>
            </div>
            <div className="flex flex-wrap gap-1">
              {context.currentSession.intents.slice(-5).map((intent, idx) => (
                <Badge key={idx} variant="secondary" className="text-xs">
                  {intent.replace(/_/g, ' ')}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Extracted Entities */}
        {Object.keys(context.currentSession.entities).length > 0 && (
          <div>
            <div className="flex items-center space-x-2 mb-2">
              <DatabaseIcon className="w-4 h-4 text-orange-400" />
              <span className="text-orange-400 text-sm font-medium">Extracted Information</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {Object.entries(context.currentSession.entities).map(([key, value], idx) => (
                <div key={idx} className="flex justify-between">
                  <span className="text-gray-400 capitalize">{key}:</span>
                  <span className="text-white truncate ml-2">{value}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Escalation Flags */}
        {context.currentSession.escalationFlags.length > 0 && (
          <div className="p-2 bg-red-500/10 rounded border border-red-500/30">
            <div className="flex items-center space-x-2 mb-1">
              <TrendingUp className="w-4 h-4 text-red-400" />
              <span className="text-red-400 text-sm font-medium">Escalation Flags</span>
            </div>
            <div className="space-y-1">
              {context.currentSession.escalationFlags.map((flag, idx) => (
                <div key={idx} className="text-xs text-red-300">
                  • {flag}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Previous Interactions */}
        {context.customerProfile.interactionHistory.length > 0 && (
          <div>
            <span className="text-blue-400 text-sm font-medium">Recent Interactions</span>
            <div className="mt-1 space-y-1">
              {context.customerProfile.interactionHistory.slice(-3).map((interaction, idx) => (
                <div key={idx} className="text-xs text-gray-300 p-2 bg-gray-800/50 rounded">
                  <div className="flex justify-between">
                    <span>{interaction.intent.replace(/_/g, ' ')}</span>
                    <span>{interaction.satisfaction}% satisfaction</span>
                  </div>
                  <div className="text-gray-500">
                    {new Date(interaction.date).toLocaleDateString()} • {Math.round(interaction.duration / 1000 / 60)}m
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Context Management Functions */}
      <div className="mt-4 pt-4 border-t border-gray-700">
        <div className="text-xs text-gray-400">
          Context functions available: addIntent(), addEntity(), updateSentiment(), addEscalationFlag()
        </div>
      </div>
    </Card>
  );
};
