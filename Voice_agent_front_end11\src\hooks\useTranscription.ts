import { useState, useEffect, useCallback } from 'react';
import { transcriptionService, LiveTranscription } from '@/services/api/TranscriptionService';

export interface UseTranscriptionOptions {
  autoStart?: boolean;
  pollingInterval?: number;
  baseUrl?: string;
}

export interface UseTranscriptionReturn {
  transcription: LiveTranscription | null;
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  startListening: () => void;
  stopListening: () => void;
  refresh: () => Promise<void>;
}

export const useTranscription = (options: UseTranscriptionOptions = {}): UseTranscriptionReturn => {
  const { 
    autoStart = true, 
    pollingInterval = 2000,
    baseUrl 
  } = options;

  const [transcription, setTranscription] = useState<LiveTranscription | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isListening, setIsListening] = useState(false);

  // Set custom base URL if provided
  useEffect(() => {
    if (baseUrl && transcriptionService['baseUrl'] !== baseUrl) {
      transcriptionService['baseUrl'] = baseUrl;
    }
  }, [baseUrl]);

  // Check backend connectivity
  const checkConnection = useCallback(async () => {
    try {
      const healthy = await transcriptionService.checkBackendHealth();
      setIsConnected(healthy);
      if (!healthy) {
        setError('Backend service is not available');
      } else {
        setError(null);
      }
      return healthy;
    } catch (err) {
      setIsConnected(false);
      setError('Failed to connect to backend service');
      return false;
    }
  }, []);

  // Manual refresh function
  const refresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const newTranscription = await transcriptionService.fetchLiveTranscription();
      setTranscription(newTranscription);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch transcription');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Start listening for live transcription updates
  const startListening = useCallback(() => {
    if (isListening) return;

    setIsListening(true);
    setIsLoading(true);
    setError(null);

    // Subscribe to updates
    const unsubscribe = transcriptionService.subscribe((newTranscription) => {
      setTranscription(newTranscription);
      setIsLoading(false);
      if (!newTranscription) {
        setError('No transcription data available');
      } else {
        setError(null);
      }
    });

    // Start polling
    transcriptionService.startPolling(pollingInterval);

    // Store cleanup function
    const cleanup = () => {
      unsubscribe();
      setIsListening(false);
      setIsLoading(false);
    };

    // Return cleanup function for internal use
    return cleanup;
  }, [isListening, pollingInterval]);

  // Stop listening for updates
  const stopListening = useCallback(() => {
    if (!isListening) return;

    transcriptionService.stopPolling();
    setIsListening(false);
    setIsLoading(false);
  }, [isListening]);

  // Auto-start if enabled
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializeConnection = async () => {
      // Check backend connection first
      const connected = await checkConnection();
      
      if (connected && autoStart) {
        cleanup = startListening();
      }
    };

    initializeConnection();

    // Cleanup on unmount
    return () => {
      if (cleanup) {
        cleanup();
      }
      stopListening();
    };
  }, [autoStart, checkConnection, startListening, stopListening]);

  // Periodic connectivity check
  useEffect(() => {
    const connectivityInterval = setInterval(checkConnection, 30000); // Check every 30 seconds
    
    return () => clearInterval(connectivityInterval);
  }, [checkConnection]);

  return {
    transcription,
    isLoading,
    isConnected,
    error,
    startListening,
    stopListening,
    refresh
  };
};