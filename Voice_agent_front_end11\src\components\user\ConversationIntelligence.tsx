
import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Brain, Target, Heart, TrendingUp, AlertCircle } from 'lucide-react';

export const ConversationIntelligence = () => {
  const [currentIntent, setCurrentIntent] = useState('Account Support');
  const [confidence, setConfidence] = useState(92);
  const [sentiment, setSentiment] = useState('Neutral');
  
  const keywords = [
    { word: 'account', weight: 'high', color: 'bg-red-500' },
    { word: 'settings', weight: 'medium', color: 'bg-yellow-500' },
    { word: 'help', weight: 'high', color: 'bg-green-500' },
    { word: 'password', weight: 'medium', color: 'bg-blue-500' }
  ];

  const conversationSummary = {
    mainTopics: ['Account Management', 'Password Reset', 'Security Settings'],
    keyPoints: [
      'User needs help with account settings',
      'Password reset required',
      'Security concerns mentioned'
    ],
    actionItems: [
      'Guide through password reset process',
      'Explain security features',
      'Provide account management tips'
    ]
  };

  return (
    <Card className="p-6 bg-white/20 backdrop-blur-sm border-white/30">
      <h3 className="text-white text-lg font-semibold mb-4 flex items-center">
        <Brain className="w-5 h-5 mr-2" />
        Conversation Intelligence
      </h3>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Intent Recognition */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-white font-medium flex items-center">
              <Target className="w-4 h-4 mr-2" />
              Intent Recognition
            </h4>
            <Badge className="bg-blue-600 text-white">{currentIntent}</Badge>
          </div>
          
          <div>
            <div className="flex justify-between mb-2">
              <span className="text-white/80 text-sm">Confidence Score</span>
              <span className="text-white text-sm">{confidence}%</span>
            </div>
            <Progress value={confidence} className="h-2" />
          </div>

          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-yellow-400" />
            <span className="text-white/80 text-sm">
              Action: Routing to account support workflow
            </span>
          </div>
        </div>

        {/* Sentiment Analysis */}
        <div className="space-y-4">
          <h4 className="text-white font-medium flex items-center">
            <Heart className="w-4 h-4 mr-2" />
            Sentiment Analysis
          </h4>
          
          <div className="flex items-center justify-between">
            <span className="text-white/80">Current Mood</span>
            <Badge 
              className={`${
                sentiment === 'Positive' ? 'bg-green-600' :
                sentiment === 'Negative' ? 'bg-red-600' : 'bg-yellow-600'
              } text-white`}
            >
              {sentiment}
            </Badge>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-white/70">Positive</span>
              <span className="text-white">30%</span>
            </div>
            <Progress value={30} className="h-1" />
            
            <div className="flex justify-between text-sm">
              <span className="text-white/70">Neutral</span>
              <span className="text-white">60%</span>
            </div>
            <Progress value={60} className="h-1" />
            
            <div className="flex justify-between text-sm">
              <span className="text-white/70">Negative</span>
              <span className="text-white">10%</span>
            </div>
            <Progress value={10} className="h-1" />
          </div>
        </div>
      </div>

      {/* Keywords Highlighting */}
      <div className="mt-6">
        <h4 className="text-white font-medium mb-3">Highlighted Keywords</h4>
        <div className="flex flex-wrap gap-2">
          {keywords.map((keyword, index) => (
            <div
              key={index}
              className={`px-3 py-1 rounded-full text-white text-sm flex items-center space-x-2 ${keyword.color}`}
            >
              <span>{keyword.word}</span>
              <Badge variant="outline" className="text-xs border-white/30 text-white">
                {keyword.weight}
              </Badge>
            </div>
          ))}
        </div>
      </div>

      {/* Conversation Summary */}
      <div className="mt-6 grid md:grid-cols-3 gap-4">
        <div>
          <h5 className="text-white font-medium mb-2">Main Topics</h5>
          <ul className="space-y-1">
            {conversationSummary.mainTopics.map((topic, index) => (
              <li key={index} className="text-white/80 text-sm">• {topic}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h5 className="text-white font-medium mb-2">Key Points</h5>
          <ul className="space-y-1">
            {conversationSummary.keyPoints.map((point, index) => (
              <li key={index} className="text-white/80 text-sm">• {point}</li>
            ))}
          </ul>
        </div>
        
        <div>
          <h5 className="text-white font-medium mb-2">Action Items</h5>
          <ul className="space-y-1">
            {conversationSummary.actionItems.map((action, index) => (
              <li key={index} className="text-white/80 text-sm">• {action}</li>
            ))}
          </ul>
        </div>
      </div>
    </Card>
  );
};
