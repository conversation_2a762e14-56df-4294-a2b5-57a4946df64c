// BackendService: Handles API, CRM, and IVR integration
import { transcriptionService } from './TranscriptionService';

export class BackendService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
  }

  async sendAudio(audioBlob: Blob): Promise<any> {
    // TODO: Send audio to backend for processing
    try {
      const formData = new FormData();
      formData.append('audio', audioBlob);

      const response = await fetch(`${this.baseUrl}/api/audio`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error sending audio:', error);
      throw error;
    }
  }

  async sendAnalytics(analytics: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(analytics),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error sending analytics:', error);
      throw error;
    }
  }

  async sendComplianceEvent(event: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/compliance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error sending compliance event:', error);
      throw error;
    }
  }

  // Voice system integration
  async getServerStatus(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/server-status`);
      return response.json();
    } catch (error) {
      console.error('Error getting server status:', error);
      throw error;
    }
  }

  async startVoiceServer(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/start-server`, {
        method: 'POST',
      });
      return response.json();
    } catch (error) {
      console.error('Error starting voice server:', error);
      throw error;
    }
  }

  async stopVoiceServer(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/stop-server`, {
        method: 'POST',
      });
      return response.json();
    } catch (error) {
      console.error('Error stopping voice server:', error);
      throw error;
    }
  }

  // Transcription integration
  async getLiveTranscription() {
    return transcriptionService.fetchLiveTranscription();
  }

  async getRecentTranscriptions() {
    return transcriptionService.fetchRecentTranscriptions();
  }

  // Model Configuration Methods
  async getAvailableModels(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/available-models`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    } catch (error) {
      console.error('Error fetching available models:', error);
      throw error;
    }
  }

  async saveModelSettings(settings: any): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/save-settings`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return response.json();
    } catch (error) {
      console.error('Error saving model settings:', error);
      throw error;
    }
  }

  async loadModelSettings(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/load-settings`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return response.json();
    } catch (error) {
      console.error('Error loading model settings:', error);
      throw error;
    }
  }

  // Health check
  async checkHealth(): Promise<boolean> {
    return transcriptionService.checkBackendHealth();
  }
}

// Export singleton instance
export const backendService = new BackendService(); 