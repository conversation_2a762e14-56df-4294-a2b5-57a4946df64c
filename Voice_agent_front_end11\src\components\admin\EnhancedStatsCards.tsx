
import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Phone, 
  Clock, 
  Users, 
  Activity, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  Shield,
  Brain,
  Globe
} from 'lucide-react';

export const EnhancedStatsCards = () => {
  const stats = [
    {
      title: 'Total Calls Today',
      value: '1,247',
      change: '+12.5%',
      trend: 'up',
      icon: <Phone className="w-6 h-6" />,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      sparkline: [45, 52, 48, 61, 58, 65, 72],
      tooltip: 'Total voice calls handled by AI agents today'
    },
    {
      title: 'Avg Response Time',
      value: '2.3s',
      change: '-0.8s',
      trend: 'up',
      icon: <Clock className="w-6 h-6" />,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      sparkline: [3.2, 2.8, 2.9, 2.5, 2.4, 2.3, 2.3],
      tooltip: 'Average time for AI to respond to user queries'
    },
    {
      title: 'Success Rate',
      value: '94.2%',
      change: '****%',
      trend: 'up',
      icon: <Activity className="w-6 h-6" />,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      sparkline: [91, 92, 93, 94, 93, 94, 94],
      tooltip: 'Percentage of calls resolved without human intervention'
    },
    {
      title: 'Active Users',
      value: '89',
      change: '+5',
      trend: 'up',
      icon: <Users className="w-6 h-6" />,
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10',
      sparkline: [82, 85, 87, 84, 86, 89, 89],
      tooltip: 'Currently active users in voice sessions'
    },
    {
      title: 'AI Confidence',
      value: '87.5%',
      change: '+1.2%',
      trend: 'up',
      icon: <Brain className="w-6 h-6" />,
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      sparkline: [85, 86, 87, 86, 88, 87, 88],
      tooltip: 'Average AI confidence score across all interactions'
    },
    {
      title: 'Error Rate',
      value: '2.8%',
      change: '-0.5%',
      trend: 'up',
      icon: <AlertTriangle className="w-6 h-6" />,
      color: 'text-red-400',
      bgColor: 'bg-red-500/10',
      sparkline: [3.5, 3.2, 3.0, 2.9, 2.8, 2.8, 2.8],
      tooltip: 'Percentage of calls that resulted in errors'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
      {stats.map((stat, index) => (
        <Card 
          key={index} 
          className="p-6 bg-gray-800/50 border-gray-700 hover:bg-gray-800/70 transition-colors group"
          title={stat.tooltip}
        >
          <div className="flex items-center justify-between mb-4">
            <div className={`p-3 rounded-lg ${stat.bgColor}`}>
              <div className={stat.color}>
                {stat.icon}
              </div>
            </div>
            <div className="flex items-center space-x-1">
              {stat.trend === 'up' ? (
                <TrendingUp className="w-4 h-4 text-green-400" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-400" />
              )}
              <span className={`text-sm font-medium ${
                stat.trend === 'up' ? 'text-green-400' : 'text-red-400'
              }`}>
                {stat.change}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <p className="text-gray-400 text-sm">{stat.title}</p>
            <p className="text-3xl font-bold text-white">{stat.value}</p>
            
            {/* Mini Sparkline */}
            <div className="flex items-end space-x-1 h-8">
              {stat.sparkline.map((value, idx) => (
                <div
                  key={idx}
                  className={`w-2 bg-gradient-to-t ${stat.bgColor.replace('/10', '/40')} rounded-sm`}
                  style={{ height: `${(value / Math.max(...stat.sparkline)) * 100}%` }}
                />
              ))}
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
};
