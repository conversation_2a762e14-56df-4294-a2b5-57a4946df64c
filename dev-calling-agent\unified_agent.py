#!/usr/bin/env python3
"""
Unified LiveKit Agent with AgenticRAG Integration and DTMF Language Selection
Combines best features from multiple files to solve deployment and functionality issues.
"""

import os
import sys
import asyncio
from typing import Dict, Any, Optional
import time
import json
from datetime import datetime
from pathlib import Path

from src.agent.agent import AgenticRAG
from src.logging.logger import logging
from src.logging.call_logger import call_logger
from src.exception import CustomException

from livekit.agents import AgentSession, Agent, JobContext, llm, stt
from livekit.plugins import silero, groq, speechify
from livekit import rtc, api
from livekit.protocol import sip as proto_sip
from dotenv import load_dotenv

load_dotenv()

# Global state management
current_call_id = None
selected_language = None
language_locked = False
rag_agent = None

# Language configuration - Fixed TTS to simba-multilingual for all languages
LANGUAGE_CONFIG = {
    '1': {
        'code': 'en',
        'name': 'English',
        'tts_voice': 'simba-multilingual',
        'instructions': """You are a helpful AI assistant. Respond clearly and naturally in English.
Use the AgenticRAG tool for all user queries - it will automatically route to either technical documentation search or web search based on the query content.
Keep responses conversational and under 50 words."""
    },
    '2': {
        'code': 'de', 
        'name': 'German',
        'tts_voice': 'simba-multilingual',
        'instructions': """Sie sind ein hilfreicher KI-Assistent. Antworten Sie klar und natürlich auf Deutsch.
WICHTIG: Verwenden Sie deutsche Wörter, aber schreiben Sie sie phonetisch mit englischen Buchstaben für bessere Aussprache.
Beispiel: "Guten Tag" als "Guten Tahg", "Wie geht es Ihnen" als "Vee gayt es Eenen"
Verwenden Sie das AgenticRAG-Tool für alle Benutzeranfragen.
Halten Sie Antworten gesprächig und unter 50 Wörtern."""
    },
    '3': {
        'code': 'tr',
        'name': 'Turkish', 
        'tts_voice': 'simba-multilingual',
        'instructions': """Yardımcı bir AI asistanısınız. Türkçe olarak açık ve doğal bir şekilde yanıt verin.
ÖNEMLİ: Türkçe kelimeler kullanın ama daha iyi telaffuz için İngilizce harflerle fonetik yazın.
Örnek: "Merhaba" as "Mer-ha-ba", "Nasılsınız" as "Na-sil-si-niz"
Tüm kullanıcı sorguları için AgenticRAG aracını kullanın.
Yanıtları konuşkan ve 50 kelime altında tutun."""
    },
    '4': {
        'code': 'hi',
        'name': 'Hindi',
        'tts_voice': 'simba-multilingual', 
        'instructions': """Aap ek sahayak AI assistant hain. Hindi mein saaf aur prakritik jawab dein.
ZAROORI: Hindi shabd istemaal karein lekin behtar uchhaaran ke liye English letters mein likhein.
Udaharan: "Namaste" as "Na-mas-te", "Kaise hain aap" as "Kai-se hain aap"
Sabhi user queries ke liye AgenticRAG tool ka upyog karein.
Jawab conversational aur 50 words ke under rakhein."""
    }
}

def initialize_rag():
    """Initialize RAG agent once at startup."""
    global rag_agent
    if rag_agent is None:
        print("🚀 Initializing AgenticRAG...")
        try:
            rag_agent = AgenticRAG()
            print("✅ AgenticRAG initialized successfully!")
        except Exception as e:
            print(f"⚠️ Failed to initialize AgenticRAG: {e}")
            rag_agent = None
    return rag_agent

def create_agentic_rag_tool():
    """Create a unified tool that wraps the AgenticRAG class."""
    
    @llm.function_tool(
        name="agentic_rag_search",
        description="Unified search tool that automatically routes queries to either technical documentation (RAG) or web search based on content. Use for all user questions."
    )
    async def agentic_rag_search(query: str) -> str:
        """Search using AgenticRAG which handles routing between RAG and web search."""
        start_time = time.time()
        
        try:
            global rag_agent, selected_language
            
            if rag_agent is None:
                rag_agent = initialize_rag()
                if rag_agent is None:
                    return "I'm sorry, the search system is currently unavailable."
            
            # Use AgenticRAG to process the query
            result = rag_agent.invoke({
                "question": query,
                "language": selected_language or 'en'
            })
            
            # Extract the answer from the result
            if isinstance(result, dict) and 'generation' in result:
                answer = result['generation']
            elif isinstance(result, str):
                answer = result
            else:
                answer = str(result)
            
            logging.info(f"✅ AgenticRAG search completed in {time.time() - start_time:.2f}s")
            return answer
            
        except Exception as e:
            logging.error(f"AgenticRAG search error: {e}")
            return "I apologize, but I encountered an issue while searching for that information."
    
    return agentic_rag_search

class UnifiedLanguageAgent(Agent):
    """Unified agent with DTMF language selection and AgenticRAG integration."""

    def __init__(self, llm_instance, tools):
        # Initial instructions for language selection
        initial_instructions = """🌍 MULTILINGUAL AI ASSISTANT 🌍

Welcome! Please select your preferred language by pressing:
- Press 1 for English
- Press 2 for German
- Press 3 for Turkish
- Press 4 for Hindi

After selecting your language, I will respond only in that language and help you with any questions using my integrated search capabilities.

Waiting for your language selection...
"""

        super().__init__(
            llm=llm_instance,
            tools=tools,
            instructions=initial_instructions
        )

        self.language_selected = False
        self._current_instructions = initial_instructions

    def update_language_instructions(self, language_code: str):
        """Update agent instructions based on selected language."""
        if language_code in LANGUAGE_CONFIG:
            config = LANGUAGE_CONFIG[language_code]
            # Store the new instructions internally
            self._current_instructions = config['instructions']
            self.language_selected = True
            logging.info(f"🌍 Language instructions updated to: {config['name']}")
            return config['instructions']
        return None

    def get_current_instructions(self):
        """Get the current instructions for the agent."""
        return self._current_instructions

class EnhancedCallSession(AgentSession):
    """Enhanced session with call logging and language-aware TTS."""

    def __init__(self, **kwargs):
        self.current_tts_voice = 'simba-english'  # Default voice
        self._tts_instance = None
        self.session_active = True
        self.current_language_instructions = None
        # Initialize base class after setting our attributes
        super().__init__(**kwargs)
        # Store initial TTS instance
        if 'tts' in kwargs:
            self._tts_instance = kwargs['tts']

    @property
    def tts(self):
        """Getter for TTS instance."""
        return self._tts_instance

    @tts.setter
    def tts(self, value):
        """Setter for TTS instance."""
        self._tts_instance = value

    def update_tts_voice(self, language_code: str):
        """Update TTS voice based on selected language."""
        if language_code in LANGUAGE_CONFIG:
            new_voice = LANGUAGE_CONFIG[language_code]['tts_voice']
            try:
                # Create new TTS instance with updated voice
                new_tts = speechify.TTS(
                    model=new_voice,
                    api_key=os.getenv("SPEECHIFY_API_KEY")
                )
                # Update the TTS instance
                self._tts_instance = new_tts
                self.current_tts_voice = new_voice
                logging.info(f"🔊 TTS voice updated to: {new_voice}")
            except Exception as e:
                logging.error(f"Failed to update TTS voice: {e}")
                # Keep the existing TTS instance if update fails
                logging.info("Continuing with previous TTS voice")

    def set_language_instructions(self, instructions: str):
        """Set the current language instructions."""
        self.current_language_instructions = instructions
        logging.info("📝 Language instructions updated in session")

    async def generate_reply(self, instructions: str = None, **kwargs):
        """Override to use current language instructions if available."""
        if self.current_language_instructions and not instructions:
            instructions = self.current_language_instructions
        elif self.current_language_instructions and instructions:
            # Combine current language instructions with specific instructions
            instructions = f"{self.current_language_instructions}\n\nSpecific instruction: {instructions}"

        return await super().generate_reply(instructions=instructions, **kwargs)

    async def wait_for_completion(self):
        """Wait for the session to complete."""
        try:
            # Keep the session alive until participant disconnects
            while self.session_active:
                await asyncio.sleep(1)
        except Exception as e:
            logging.error(f"Session completion error: {e}")
        finally:
            self.session_active = False

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the unified agent."""
    global current_call_id, selected_language, language_locked
    
    try:
        start_time = time.time()
        
        # Initialize RAG agent at startup
        initialize_rag()
        
        # Reset global state for new call
        selected_language = None
        language_locked = False
        current_call_id = None
        
        # Connect to room
        await ctx.connect(auto_subscribe=True)
        
        # Wait for participant
        participant = await ctx.wait_for_participant()
        
        # Check if this is a SIP call and start call logging
        is_sip_call = participant.attributes.get('sip.callId') is not None
        if is_sip_call:
            current_call_id = call_logger.start_call(participant)
            if current_call_id:
                print(f"📞 SIP Call detected - Call ID: {current_call_id}")
                logging.info(f"Call logging started for call ID: {current_call_id}")
        
        # Create unified AgenticRAG tool
        agentic_tool = create_agentic_rag_tool()

        # Create session with dynamic configuration
        session = EnhancedCallSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                detect_language=True,
                api_key=os.getenv("GROQ_API_KEY")
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
                temperature=0.1,
                api_key=os.getenv("GROQ_API_KEY")
            ),
            tts=speechify.TTS(
                model="simba-english",  # Default, will be updated based on selection
                api_key=os.getenv("SPEECHIFY_API_KEY")
            ),
        )

        # Create agent with unified tool
        agent = UnifiedLanguageAgent(
            llm_instance=session.llm,
            tools=[agentic_tool]
        )

        # Setup DTMF handler for language selection
        @ctx.room.on("sip_dtmf_received")
        def handle_dtmf(dtmf_event: rtc.SipDTMF):
            """Handle DTMF input for language selection."""
            global selected_language, language_locked

            digit = dtmf_event.digit
            logging.info(f"📱 DTMF received: {digit}")

            if not language_locked and digit in LANGUAGE_CONFIG:
                selected_language = LANGUAGE_CONFIG[digit]['code']
                language_name = LANGUAGE_CONFIG[digit]['name']
                language_locked = True

                # Update agent instructions
                new_instructions = agent.update_language_instructions(digit)
                if new_instructions:
                    # Set the language instructions in the session
                    session.set_language_instructions(new_instructions)
                    
                    # Ensure message processing remains active
                    session.ensure_message_processing()

                    # Log language selection
                    if current_call_id:
                        call_logger.set_language(current_call_id, f"{language_name} ({selected_language})")

                    # Confirm language selection with non-blocking task
                    confirmation_msg = {
                        'en': f"Great! I will now respond in {language_name}. How can I help you?",
                        'de': f"Gross-ar-tig! Ich ver-de yetzt auf {language_name} ant-vor-ten. Vee kann ich Ee-nen hel-fen?",
                        'tr': f"Ha-ri-ka! Shim-di {language_name} di-lin-de ya-nit ve-re-je-jim. Si-ze na-sil yar-dim-ji o-la-bi-li-rim?",
                        'hi': f"Ba-hut ach-cha! Main ab {language_name} mein ja-wab dun-ga. Main aap-ki kai-se ma-dad kar sak-ta hun?"
                    }

                    # Create non-blocking confirmation task
                    async def send_confirmation():
                        try:
                            await session.generate_reply(
                                instructions=f"Say exactly: '{confirmation_msg.get(selected_language, confirmation_msg['en'])}'"
                            )
                            logging.info(f"✅ Language confirmation sent for: {language_name}")
                        except Exception as e:
                            logging.error(f"Failed to send confirmation: {e}")

                    # Schedule confirmation without blocking
                    asyncio.create_task(send_confirmation())
                    logging.info(f"🌍 Language locked to: {language_name} ({selected_language})")

            elif language_locked:
                # Language already selected, ignore further DTMF
                logging.info("🔒 Language already selected, ignoring DTMF")

            else:
                # Invalid selection - non-blocking response
                async def send_invalid_selection():
                    try:
                        await session.generate_reply(
                            instructions="Say: 'Please press 1 for English, 2 for German, 3 for Turkish, or 4 for Hindi.'"
                        )
                    except Exception as e:
                        logging.error(f"Failed to send invalid selection message: {e}")
                
                asyncio.create_task(send_invalid_selection())

        # Start session
        await session.start(agent=agent, room=ctx.room)

        print(f"✅ Unified agent ready in {time.time() - start_time:.2f}s")

        # Send initial language selection prompt
        try:
            await session.generate_reply(
                instructions="Say in English: 'Welcome! Please select your preferred language by pressing: Press 1 for English, Press 2 for German, Press 3 for Turkish, Press 4 for Hindi.' Keep it clear and friendly."
            )
            logging.info("🎯 Initial language prompt sent")
        except Exception as e:
            logging.error(f"Failed to send initial prompt: {e}")

        # Keep session alive and responsive
        try:
            while session.session_active:
                await asyncio.sleep(0.1)  # Small sleep to prevent busy waiting
                
                # Check if participant is still connected
                if not ctx.room.participants:
                    logging.info("📞 No participants, ending session")
                    break
                    
        except Exception as e:
            logging.error(f"Session monitoring error: {e}")
        finally:
            session.session_active = False
            if current_call_id:
                call_logger.end_call(current_call_id)
                logging.info(f"📞 Call {current_call_id} ended")

    except Exception as e:
        logging.error(f"Entrypoint error: {e}")
        if current_call_id:
            call_logger.end_call(current_call_id)
        raise CustomException(e, sys)  # Add sys to the exception

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    
    # Create logs directory
    os.makedirs("call_logs", exist_ok=True)
    
    print("🎙️ Starting Unified LiveKit Agent...")
    
    # Run the agent
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="telephony_agent"))


