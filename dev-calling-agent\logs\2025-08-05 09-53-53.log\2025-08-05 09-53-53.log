[ 2025-08-05 09:53:57,585 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-4-maverick-17b-128e-instruct', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-05 09:53:57,585 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-05 09:53:58,060 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4757 seconds.
[ 2025-08-05 09:54:02,325 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-base-en-v1.5
[ 2025-08-05 09:54:12,161 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 09:54:12,162 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 09:54:12,162 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-05 09:55:23,150 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-05 09:55:23,150 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-05 09:55:23,150 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-05 09:55:23,152 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-05 09:55:23,165 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-05 09:55:23,233 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,234 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,237 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,238 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,240 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,240 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,243 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,246 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,250 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,251 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,252 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,253 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:23,286 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,287 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,288 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,288 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,288 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,288 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,288 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,289 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,289 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,290 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,290 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,292 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:23,977 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-05 09:55:35,184 ] 855 livekit.agents - INFO - received job request
[ 2025-08-05 09:55:36,517 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-05 09:55:36,518 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-05 09:55:38,032 ] 666 root - INFO - Call logging started for call ID: SCL_kuZuiD4FFeZZ
[ 2025-08-05 09:55:40,633 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mnzrf7vt0navz112tnjm, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:40,971 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mp99f7vtv02082xnqp1z, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:43,106 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mrdffejsnr0bq42wq4n3, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:43,915 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-05 09:55:43,920 ] 755 root - INFO - Call logging ended for call ID: SCL_kuZuiD4FFeZZ
[ 2025-08-05 09:55:43,921 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-05 09:55:44,859 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mt46f7wavv6mz7gkxt0q, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:45,076 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mtb3fekvcvzx4hqvxk51, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:47,177 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mwcrfepa5vs95rtqahzd, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:49,312 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7myfdfep9qx2gpjxkydj7, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-05 09:55:50,409 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mzhwf7xbxz4tdg5w6x1b, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:50,661 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7mzskf5kvghahbc6gkr19, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:52,846 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7n1w9feqt1bf2r5k5b97q, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-05 09:55:55,024 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1w7n40hf7xvcmd2a4am80e8, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-05 09:55:57,457 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-05 09:56:21,316 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-05 09:56:56,688 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-05 09:56:56,689 ] 560 livekit.agents - INFO - shutting down worker
