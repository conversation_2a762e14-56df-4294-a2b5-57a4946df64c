
import React from 'react';
import { Card } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Download } from 'lucide-react';

export const CallLogsTable = () => {
  const callLogs = [
    {
      id: 'CL-2024-001',
      timestamp: '2024-01-15 14:32:15',
      duration: '2:45',
      language: 'EN',
      status: 'Completed',
      aiConfidence: '96%',
      transferred: false
    },
    {
      id: 'CL-2024-002',
      timestamp: '2024-01-15 14:28:32',
      duration: '1:12',
      language: 'DE',
      status: 'Transferred',
      aiConfidence: '78%',
      transferred: true
    },
    {
      id: 'CL-2024-003',
      timestamp: '2024-01-15 14:25:18',
      duration: '3:21',
      language: 'TR',
      status: 'Completed',
      aiConfidence: '94%',
      transferred: false
    },
    {
      id: 'CL-2024-004',
      timestamp: '2024-01-15 14:20:45',
      duration: '0:58',
      language: 'EN',
      status: 'Completed',
      aiConfidence: '98%',
      transferred: false
    }
  ];

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-white">Recent Call Logs</h3>
        <Button variant="outline" size="sm" className="bg-gradient-to-r from-pink-500 to-purple-600 text-white">
          <Download className="w-4 h-4 mr-2" />
          Export Logs
        </Button>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-700 hover:bg-transparent">
              <TableHead className="text-gray-300">Call ID</TableHead>
              <TableHead className="text-gray-300">Timestamp</TableHead>
              <TableHead className="text-gray-300">Duration</TableHead>
              <TableHead className="text-gray-300">Language</TableHead>
              <TableHead className="text-gray-300">Status</TableHead>
              <TableHead className="text-gray-300">AI Confidence</TableHead>
              <TableHead className="text-gray-300">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {callLogs.map((log) => (
              <TableRow key={log.id} className="border-gray-800 hover:bg-gray-900/30">
                <TableCell className="text-white font-mono">{log.id}</TableCell>
                <TableCell className="text-gray-300">{log.timestamp}</TableCell>
                <TableCell className="text-gray-300">{log.duration}</TableCell>
                <TableCell>
                  <Badge variant="outline" className="text-blue-400 border-blue-400">
                    {log.language}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={log.transferred ? "secondary" : "default"}
                    className={log.transferred ? "bg-yellow-500/20 text-yellow-400" : "bg-green-500/20 text-green-400"}
                  >
                    {log.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-gray-300">{log.aiConfidence}</TableCell>
                <TableCell>
                  <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                    <Eye className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </Card>
  );
};
