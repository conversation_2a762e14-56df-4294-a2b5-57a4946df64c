[ 2025-08-07 17:35:36,191 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'HwGQf9BXnooaNMX680n7zPW_bxVL7Tw7bBu_3gRxiho='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 17:35:36,191 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 17:35:36,595 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4041 seconds.
[ 2025-08-07 17:35:40,716 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 17:35:49,904 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 17:35:49,905 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 17:35:49,906 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 17:35:51,366 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 17:35:51,366 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 17:35:51,366 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 17:35:51,368 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 17:35:51,374 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 17:35:51,405 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,408 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,411 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,414 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,416 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,416 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,417 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,418 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,418 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,418 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,418 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,419 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:35:51,615 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,615 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,616 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:51,617 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:35:52,119 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 17:36:52,322 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:36:54,834 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:39:59,603 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 17:39:59,850 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 17:39:59,852 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 17:40:02,319 ] 666 root - INFO - Call logging started for call ID: SCL_YwWNPWLtSGwp
[ 2025-08-07 17:40:05,447 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:40:11,172 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 17:40:11,192 ] 755 root - INFO - Call logging ended for call ID: SCL_YwWNPWLtSGwp
[ 2025-08-07 17:40:11,196 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 17:40:12,962 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:40:17,993 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:40:20,391 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 17:40:20,393 ] 207 root - INFO - \u26a1 Language detection: 0.922s
[ 2025-08-07 17:40:20,394 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 17:40:20,394 ] 513 root - INFO - Enhanced web search query: current Prime Minister Ganesh as of 2025 as of 2025
[ 2025-08-07 17:40:20,396 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:40:20,399 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister Ganesh ...'
[ 2025-08-07 17:40:20,502 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:40:23,027 ] 59 root - INFO - \u26a1 Web search: 2.63s, 2 results
[ 2025-08-07 17:40:23,030 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6338 seconds.
[ 2025-08-07 17:40:23,034 ] 525 root - INFO - \U0001f310 Web search: 3.56s, 2 results
[ 2025-08-07 17:40:23,573 ] 513 root - INFO - Enhanced web search query: Gyanesh Kumar Prime Minister as of 2025 as of 2025
[ 2025-08-07 17:40:23,575 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 17:40:23,578 ] 45 root - INFO - \U0001f310 Fast web search: 'Gyanesh Kumar Prime Minister a...'
[ 2025-08-07 17:40:25,885 ] 59 root - INFO - \u26a1 Web search: 2.31s, 2 results
[ 2025-08-07 17:40:25,887 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3114 seconds.
[ 2025-08-07 17:40:25,889 ] 525 root - INFO - \U0001f310 Web search: 2.32s, 2 results
[ 2025-08-07 17:40:33,517 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 17:40:33,519 ] 37 root - INFO - \U0001f50d Vector DB search for: 'effective methods for preventing hair loss...'
[ 2025-08-07 17:40:36,596 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 17:40:36,602 ] 159 root - INFO - \u26a1 New response generated in 3.08s
[ 2025-08-07 17:40:36,605 ] 51 root - INFO - Vector DB search completed in 3.09s
[ 2025-08-07 17:40:36,610 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 17:40:36,619 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.1012 seconds.
[ 2025-08-07 17:40:36,622 ] 457 root - INFO - \u2705 Vector search: 3.11s
[ 2025-08-07 17:40:40,388 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 17:40:40,482 ] 465 livekit.agents - WARNING - rotate_segment called while previous segment is still being rotated
[ 2025-08-07 17:41:03,601 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 17:43:15,988 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:43:18,475 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:43:23,510 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:43:31,059 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:43:48,599 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:43:51,127 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:44:18,700 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:44:21,220 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:44:36,308 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:44:38,809 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:44:46,343 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 17:44:48,844 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 17:45:43,517 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 17:45:43,520 ] 560 livekit.agents - INFO - shutting down worker
