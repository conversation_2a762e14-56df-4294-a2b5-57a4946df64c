// AudioLevelsService: Real-time audio levels from active calls
export interface AudioLevelsResponse {
  status: string;
  audio_level: number;
  has_active_calls: boolean;
  active_call_count: number;
  timestamp: string;
}

export class AudioLevelsService {
  private baseUrl: string;
  private pollingInterval: NodeJS.Timeout | null = null;
  private subscribers: Set<(audioLevel: number, hasActiveCalls: boolean) => void> = new Set();
  private lastAudioLevel: number = 0;
  private lastFetch: number = 0;
  private minFetchInterval: number = 500; // 500ms for smooth audio level updates

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
  }

  /**
   * Fetch current audio levels from backend
   */
  async fetchAudioLevels(): Promise<{ audioLevel: number; hasActiveCalls: boolean }> {
    const now = Date.now();
    
    // Skip fetch if too soon since last fetch
    if (now - this.lastFetch < this.minFetchInterval) {
      return { audioLevel: this.lastAudioLevel, hasActiveCalls: false };
    }

    try {
      this.lastFetch = now;
      
      const response = await fetch(`${this.baseUrl}/api/audio-levels`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: AudioLevelsResponse = await response.json();
      
      if (data.status === 'success') {
        this.lastAudioLevel = data.audio_level;
        return { 
          audioLevel: data.audio_level, 
          hasActiveCalls: data.has_active_calls 
        };
      }
      
      return { audioLevel: 0, hasActiveCalls: false };
    } catch (error) {
      console.error('Error fetching audio levels:', error);
      // Fallback to previous level or 0
      return { audioLevel: this.lastAudioLevel, hasActiveCalls: false };
    }
  }

  /**
   * Subscribe to audio level updates
   */
  subscribe(callback: (audioLevel: number, hasActiveCalls: boolean) => void): () => void {
    this.subscribers.add(callback);
    
    return () => {
      this.subscribers.delete(callback);
      if (this.subscribers.size === 0) {
        this.stopPolling();
      }
    };
  }

  /**
   * Start polling for audio level updates
   */
  startPolling(intervalMs: number = 200): void { // Fast polling for smooth audio visualization
    if (this.pollingInterval) {
      return;
    }

    const poll = async () => {
      const { audioLevel, hasActiveCalls } = await this.fetchAudioLevels();
      this.notifySubscribers(audioLevel, hasActiveCalls);
    };

    // Initial fetch
    poll();

    // Set up polling interval
    this.pollingInterval = setInterval(poll, Math.max(intervalMs, this.minFetchInterval));
  }

  /**
   * Stop polling for updates
   */
  stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  /**
   * Notify all subscribers of new audio level data
   */
  private notifySubscribers(audioLevel: number, hasActiveCalls: boolean): void {
    this.subscribers.forEach(callback => {
      try {
        callback(audioLevel, hasActiveCalls);
      } catch (error) {
        console.error('Error in audio levels subscriber callback:', error);
      }
    });
  }

  /**
   * Check if backend is available
   */
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Backend health check failed:', error);
      return false;
    }
  }

  /**
   * Destroy the service and clean up resources
   */
  destroy(): void {
    this.stopPolling();
    this.subscribers.clear();
  }
}

// Singleton instance
export const audioLevelsService = new AudioLevelsService();