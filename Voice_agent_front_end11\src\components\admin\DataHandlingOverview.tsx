
import React from 'react';
import { Card } from '@/components/ui/card';
import { Database, Lock, MapPin, Clock } from 'lucide-react';

export const DataHandlingOverview = () => {
  const dataCategories = [
    {
      category: 'Voice Data',
      retention: '30 days',
      encryption: 'AES-256',
      location: 'EU-Central'
    },
    {
      category: 'Transcripts',
      retention: '90 days',
      encryption: 'AES-256',
      location: 'EU-Central'
    },
    {
      category: 'Call Metadata',
      retention: '2 years',
      encryption: 'AES-256',
      location: 'EU-Central'
    },
    {
      category: 'User Analytics',
      retention: '1 year',
      encryption: 'AES-256',
      location: 'EU-Central'
    }
  ];

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center space-x-3 mb-6">
        <Database className="w-6 h-6 text-blue-400" />
        <h3 className="text-xl font-semibold text-white">Data Handling Overview</h3>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-700">
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Data Category</th>
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Retention Period</th>
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Encryption</th>
              <th className="text-left py-3 px-4 text-gray-300 font-medium">Storage Location</th>
            </tr>
          </thead>
          <tbody>
            {dataCategories.map((item, index) => (
              <tr key={index} className="border-b border-gray-800 hover:bg-gray-900/30">
                <td className="py-3 px-4 text-white">{item.category}</td>
                <td className="py-3 px-4 text-gray-300">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-400" />
                    <span>{item.retention}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-gray-300">
                  <div className="flex items-center space-x-2">
                    <Lock className="w-4 h-4 text-green-400" />
                    <span>{item.encryption}</span>
                  </div>
                </td>
                <td className="py-3 px-4 text-gray-300">
                  <div className="flex items-center space-x-2">
                    <MapPin className="w-4 h-4 text-purple-400" />
                    <span>{item.location}</span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="mt-6 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
        <h4 className="text-blue-400 font-medium mb-2">Your Data Rights</h4>
        <p className="text-gray-300 text-sm">
          You have the right to access, rectify, delete, or port your data. All voice interactions are processed 
          in compliance with GDPR and EU AI Act requirements. Data is anonymized for analytics and deleted 
          according to our retention policies.
        </p>
      </div>
    </Card>
  );
};
