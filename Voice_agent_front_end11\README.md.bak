# AI Voice Agent Frontend

Enterprise-ready, compliant, and extensible AI Voice Agent frontend for telecom and enterprise use cases.

## Features
- Multilingual support (German, English, Turkish, etc.)
- Real-time speech processing (low latency)
- Contextual conversation (follow-up, cross-interaction)
- Call routing/escalation to human agents
- Natural Language Understanding (NLU)
- Sentiment analysis (real-time, dialect-aware)
- Call analytics dashboard (duration, resolution, feedback, etc.)
- CRM/IVR integration hooks
- GDPR, CCPA, ISO 27001, eIDAS, EU AI Act, PCI DSS compliance
- Bot/human transparency
- Settings/config management

## Setup
1. Clone the repo
2. Copy `.env.example` to `.env` and fill in required values
3. `npm install`
4. `npm run dev`

## Compliance
- All data processed/stored in EU (Cologne/Azure)
- Consent management and audit logging
- PII redaction and export

## Extension
- Add new AI/NLU providers in `AIService`
- Add new compliance events in `ComplianceService`
- Integrate with CRM/IVR via `BackendService`

## Test Harness
- See `TestVoiceAgent` page for end-to-end testing

---

**For questions, see CTO notes in the codebase or contact the  team.**
