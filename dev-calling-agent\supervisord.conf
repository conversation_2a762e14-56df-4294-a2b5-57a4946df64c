[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:fastapi]
command=uvicorn app:app --host 0.0.0.0 --port 8000
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/fastapi.err.log
stdout_logfile=/var/log/supervisor/fastapi.out.log
environment=PYTHONUNBUFFERED=1

[program:langgraph]
command=/app/start.sh
directory=/app
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/langgraph.err.log
stdout_logfile=/var/log/supervisor/langgraph.out.log
environment=PYTHONUNBUFFERED=1
