import { useState, useEffect, useCallback } from 'react';
import { audioLevelsService } from '@/services/api/AudioLevelsService';

export interface UseAudioLevelsOptions {
  autoStart?: boolean;
  pollingInterval?: number;
  simulateFallback?: boolean;
}

export interface UseAudioLevelsReturn {
  audioLevel: number;
  hasActiveCalls: boolean;
  isConnected: boolean;
  error: string | null;
  startListening: () => void;
  stopListening: () => void;
  refresh: () => Promise<void>;
}

export const useAudioLevels = (options: UseAudioLevelsOptions = {}): UseAudioLevelsReturn => {
  const { 
    autoStart = true, 
    pollingInterval = 200,
    simulateFallback = true
  } = options;

  const [audioLevel, setAudioLevel] = useState<number>(0);
  const [hasActiveCalls, setHasActiveCalls] = useState<boolean>(false);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isListening, setIsListening] = useState<boolean>(false);
  const [simulationInterval, setSimulationInterval] = useState<NodeJS.Timeout | null>(null);

  // Check backend connectivity
  const checkConnection = useCallback(async () => {
    try {
      const healthy = await audioLevelsService.checkBackendHealth();
      setIsConnected(healthy);
      if (!healthy) {
        setError('Backend service is not available');
      } else {
        setError(null);
      }
      return healthy;
    } catch (err) {
      setIsConnected(false);
      setError('Failed to connect to backend service');
      return false;
    }
  }, []);

  // Start simulation fallback when backend is not available
  const startSimulation = useCallback(() => {
    if (simulationInterval || !simulateFallback) return;

    const interval = setInterval(() => {
      // Simulate audio levels with some variation
      const newLevel = Math.random() * 100;
      setAudioLevel(newLevel);
      setHasActiveCalls(false); // No real calls in simulation
    }, pollingInterval);

    setSimulationInterval(interval);
  }, [simulationInterval, simulateFallback, pollingInterval]);

  // Stop simulation
  const stopSimulation = useCallback(() => {
    if (simulationInterval) {
      clearInterval(simulationInterval);
      setSimulationInterval(null);
    }
  }, [simulationInterval]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    try {
      const { audioLevel: newLevel, hasActiveCalls: activeCalls } = await audioLevelsService.fetchAudioLevels();
      setAudioLevel(newLevel);
      setHasActiveCalls(activeCalls);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch audio levels');
    }
  }, []);

  // Start listening for real audio level updates
  const startListening = useCallback(() => {
    if (isListening) return;

    setIsListening(true);
    setError(null);

    // Subscribe to updates
    const unsubscribe = audioLevelsService.subscribe((newAudioLevel, activeCalls) => {
      setAudioLevel(newAudioLevel);
      setHasActiveCalls(activeCalls);
      setError(null);
    });

    // Start polling
    audioLevelsService.startPolling(pollingInterval);

    // Return cleanup function
    return () => {
      unsubscribe();
      setIsListening(false);
    };
  }, [isListening, pollingInterval]);

  // Stop listening for updates
  const stopListening = useCallback(() => {
    if (!isListening) return;

    audioLevelsService.stopPolling();
    setIsListening(false);
  }, [isListening]);

  // Auto-start logic
  useEffect(() => {
    let cleanup: (() => void) | undefined;

    const initializeAudioLevels = async () => {
      const connected = await checkConnection();
      
      if (connected && autoStart) {
        // Use real data from backend
        stopSimulation();
        cleanup = startListening();
      } else if (simulateFallback && autoStart) {
        // Fall back to simulation
        startSimulation();
      }
    };

    initializeAudioLevels();

    return () => {
      if (cleanup) cleanup();
      stopListening();
      stopSimulation();
    };
  }, [autoStart, checkConnection, startListening, stopListening, simulateFallback, startSimulation, stopSimulation]);

  // Periodic connectivity check
  useEffect(() => {
    const connectivityInterval = setInterval(checkConnection, 30000); // Check every 30 seconds
    
    return () => clearInterval(connectivityInterval);
  }, [checkConnection]);

  return {
    audioLevel,
    hasActiveCalls,
    isConnected,
    error,
    startListening,
    stopListening,
    refresh
  };
};