
import React from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Webhook } from 'lucide-react';

export const WebhooksConfiguration: React.FC = () => {
  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
        <Webhook className="w-5 h-5 mr-2" />
        Webhook Configuration
      </h3>
      <div className="space-y-4">
        <div>
          <Label className="text-gray-300">Global Webhook URL</Label>
          <Input
            value="https://voice-ai.ll-aisolutions.com/api/webhooks"
            readOnly
            className="bg-gray-900 border-gray-700 text-white"
          />
        </div>
        
        <div>
          <Label className="text-gray-300">Supported Events</Label>
          <div className="grid grid-cols-2 gap-2 mt-2">
            {[
              'call.started',
              'call.ended',
              'intent.detected',
              'escalation.triggered',
              'resolution.completed',
              'customer.updated',
              'integration.sync',
              'data.processed',
              'compliance.check',
              'security.alert'
            ].map((event) => (
              <div key={event} className="flex items-center space-x-2">
                <Switch defaultChecked />
                <span className="text-gray-300 text-sm">{event}</span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <Label className="text-gray-300">Webhook Secret</Label>
          <Input
            type="password"
            placeholder="Enter webhook secret for validation"
            className="bg-gray-900 border-gray-700 text-white"
          />
        </div>
      </div>
    </Card>
  );
};
