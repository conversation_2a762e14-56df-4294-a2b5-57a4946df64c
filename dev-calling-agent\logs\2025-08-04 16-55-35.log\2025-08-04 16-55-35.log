[ 2025-08-04 16:55:38,951 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-4-maverick-17b-128e-instruct', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 16:55:38,951 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 16:55:39,492 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5406 seconds.
[ 2025-08-04 16:55:45,426 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: BAAI/bge-base-en-v1.5
[ 2025-08-04 16:55:59,154 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 16:55:59,155 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 16:55:59,155 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 16:57:10,460 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 16:57:10,461 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 16:57:10,461 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 16:57:10,461 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 16:57:10,493 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 16:57:10,563 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 16:57:10,586 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 16:57:10,590 ] 633 asyncio - DEBUG - Using proactor: IocpProactor
[ 2025-08-04 16:57:10,590 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 16:57:13,279 ] 546 livekit.agents - DEBUG - using audio io: `ChatCLI` -> `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 16:57:13,280 ] 552 livekit.agents - DEBUG - using transcript io: `AgentSession` -> `TranscriptSynchronizer` -> `ChatCLI`
[ 2025-08-04 16:57:14,652 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdc2dqej48jq0m73kdsfem, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:15,034 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdc2smednvj38nj669jjkt, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:17,218 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdc4wjej4a3bc8rkd7kb5b, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:18,687 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 16:57:18,689 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 16:57:21,007 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:21,210 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdc8rxej4903485amjv246, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:21,471 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdc938es6bnwxrp0mt37ej, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:26,838 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:26,970 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdceetes69fdney971rnr5, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:27,247 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdceqjes68dyay5vkh5msj, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:29,367 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcgsqej7ajxkkp8zm8v76, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:31,550 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcjx5edx83yq4q2het0dk, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-04 16:57:35,439 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:35,648 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcpw7es897wewamjkf2gj, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:35,870 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcq59ee1adm9e066bydqw, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:39,711 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:40,424 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcvkjejbskge598f0sb93, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:40,768 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcvy2ejcaqp5d3cvhy2qn, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:40,878 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:40,998 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcw5gejcvf3sa7ate3nhr, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:41,248 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdcwd2ejdb39m2mh6vfevg, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:44,543 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:44,762 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdczs6eje9sn4hgkrrscd1, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:45,069 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd038ejersqv1drxkwarx, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:47,222 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd282ee983z56vc5np193, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:49,633 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:49,776 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd4q1eears8mkq91ny7cw, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:50,028 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd4zqeeat7h05mm01f32k, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:53,384 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:53,997 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd8vseed9r223nrtv7m1t, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:54,419 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd98wejhb6qtyec0zc8t5, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:54,688 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:57:54,830 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd9npejhbtawee61gnxtx, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:55,186 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdd9xjeeda94zsmg6ph59y, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:57,353 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tddc4ceed82278ngd9m6fk, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:57:59,574 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdde83ejm958qefnkyjbxj, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-04 16:58:22,852 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:24,823 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tde6z3ejwtph13m539g29v, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:25,515 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tde7k5eesbarvfqq92bbn9, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:25,642 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:28,068 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdea46ejxvwt0kgj4y0ryg, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:29,100 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:31,684 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdedndesn8dx4gcrph7a1d, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:31,908 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:32,236 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdee6mejz8tkdjk0h1smc1, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:33,649 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:34,062 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdefzmejzrw2nd1tj550s7, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:34,423 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdeg8zesnb8rv3gam3c59h, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:36,598 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdejepesnvssk32etfc8pg, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:38,321 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:38,622 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdemd5eexaxvt5mfnvdycw, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:39,024 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdemtpeey99vvnmm7fc8dq, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:39,649 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:41,911 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdeqmjesqaq89j8g0b8aak, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:42,633 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tderbmek58mjyne3kxs9bb, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:43,002 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:44,164 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdesv9esqrkzebpacvy7re, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:44,767 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdetdjesqtn8a92a957qe3, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:44,938 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:45,122 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdetsaef1tn3n7kkzv790a, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:45,385 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdev1kef2808ds5k57bw8x, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:47,039 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:47,222 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdewt2ef2rcq49gthe1xfr, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:47,529 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdex30ef2t8zpx0xfrfdyq, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:49,702 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdez8dek89qzev3bw2b0sw, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:51,897 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdf1c4ef39t6jqjpn75jxr, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-04 16:58:55,998 ] 256 livekit.agents - DEBUG - received user transcript
[ 2025-08-04 16:58:56,234 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdf5j3esrvcw6fv37j2j8s, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:56,540 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdf5wyef5s7dtshm5fts30, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:58:58,739 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdf82gestrtdnyhbqy40rk, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:59:00,953 ] 877 livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdfa7zef88cmycxmmzrh5w, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)
[ 2025-08-04 16:59:00,956 ] 21 livekit.agents - ERROR - Error in _llm_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 404 - {'error': {'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}} (status_code=404, request_id=req_01k1tdfa7zef88cmycxmmzrh5w, body={'message': 'The model `llama-4-maverick-17b-128e-instruct` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}, retryable=True)

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 124, in _llm_inference_task
    async for chunk in llm_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 378, in llm_node
    async for chunk in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 325, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 183, in _main_task
    raise APIConnectionError(
livekit.agents._exceptions.APIConnectionError: failed to generate LLM completion after 4 attempts (body=None, retryable=True)
[ 2025-08-04 16:59:00,961 ] 650 livekit.agents - DEBUG - session closed
[ 2025-08-04 17:03:54,634 ] 560 livekit.agents - INFO - shutting down worker
[ 2025-08-04 17:03:54,635 ] 278 livekit.agents - DEBUG - shutting down job task
[ 2025-08-04 17:03:54,638 ] 57 livekit.agents - DEBUG - http_session(): closing the httpclient ctx
[ 2025-08-04 17:03:54,639 ] 278 livekit.agents - DEBUG - job exiting
[ 2025-08-04 17:03:54,639 ] 20 livekit.agents - DEBUG - http_session(): creating a new httpclient ctx
