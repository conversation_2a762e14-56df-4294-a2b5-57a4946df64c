[ 2025-08-11 15:30:25,459 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'HwGQf9BXnooaNMX680n7zPW_bxVL7Tw7bBu_3gRxiho='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-11 15:30:25,459 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 15:30:25,958 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4985 seconds.
[ 2025-08-11 15:30:32,665 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 15:30:43,961 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 15:30:43,961 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 15:30:43,962 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 15:30:44,890 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 15:30:44,891 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 15:30:44,891 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-11 15:30:44,891 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-11 15:30:44,897 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 15:30:44,956 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,959 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,959 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,960 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,961 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,961 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,962 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,962 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,962 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,962 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,963 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:44,963 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:30:45,162 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,162 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,162 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,163 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,164 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,164 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,164 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:30:45,719 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 15:33:11,145 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:33:16,140 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:36:54,287 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:36:56,799 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:37:01,812 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:37:04,308 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:37:07,228 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 15:37:07,403 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 15:37:07,405 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 15:37:09,636 ] 666 root - INFO - Call logging started for call ID: SCL_mVLtjs8MLmhu
[ 2025-08-11 15:37:15,539 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-11 15:37:15,544 ] 755 root - INFO - Call logging ended for call ID: SCL_mVLtjs8MLmhu
[ 2025-08-11 15:37:15,545 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-11 15:37:26,876 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:37:29,388 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:37:49,735 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-11 15:37:49,737 ] 207 root - INFO - \u26a1 Language detection: 0.638s
[ 2025-08-11 15:37:49,738 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-11 15:37:49,738 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-11 15:37:49,739 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:37:49,740 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-11 15:37:52,441 ] 59 root - INFO - \u26a1 Web search: 2.70s, 2 results
[ 2025-08-11 15:37:52,442 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7029 seconds.
[ 2025-08-11 15:37:52,443 ] 525 root - INFO - \U0001f310 Web search: 3.34s, 2 results
[ 2025-08-11 15:37:52,779 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-11 15:37:52,780 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:37:52,780 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-11 15:37:55,153 ] 59 root - INFO - \u26a1 Web search: 2.37s, 2 results
[ 2025-08-11 15:37:55,153 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3725 seconds.
[ 2025-08-11 15:37:55,153 ] 525 root - INFO - \U0001f310 Web search: 2.37s, 2 results
[ 2025-08-11 15:37:57,498 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-11 15:37:57,499 ] 513 root - INFO - Enhanced web search query: Prime Minister of India 2025 as of 2025
[ 2025-08-11 15:37:57,500 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:37:57,501 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-11 15:37:57,504 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:37:57,507 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India 2025 a...'
[ 2025-08-11 15:37:57,842 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 15:37:59,151 ] 666 root - INFO - Call logging started for call ID: SCL_6ZwnuXg4jPcd
[ 2025-08-11 15:37:59,522 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:37:59,791 ] 59 root - INFO - \u26a1 Web search: 2.28s, 2 results
[ 2025-08-11 15:37:59,792 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2877 seconds.
[ 2025-08-11 15:37:59,794 ] 525 root - INFO - \U0001f310 Web search: 2.29s, 2 results
[ 2025-08-11 15:38:00,593 ] 59 root - INFO - \u26a1 Web search: 3.09s, 2 results
[ 2025-08-11 15:38:00,595 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.0950 seconds.
[ 2025-08-11 15:38:00,601 ] 525 root - INFO - \U0001f310 Web search: 3.10s, 2 results
[ 2025-08-11 15:38:04,558 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:38:04,566 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-11 15:38:04,575 ] 755 root - INFO - Call logging ended for call ID: SCL_6ZwnuXg4jPcd
[ 2025-08-11 15:38:04,577 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-11 15:38:06,567 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-11 15:38:06,571 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-11 15:38:06,575 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-11 15:38:06,578 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-11 15:38:09,893 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:38:10,513 ] 513 root - INFO - Enhanced web search query: premise of the year 2025 as of 2025 as of 2025
[ 2025-08-11 15:38:10,514 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:10,517 ] 45 root - INFO - \U0001f310 Fast web search: 'premise of the year 2025 as of...'
[ 2025-08-11 15:38:11,809 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-11 15:38:11,812 ] 159 root - INFO - \u26a1 New response generated in 5.22s
[ 2025-08-11 15:38:11,813 ] 51 root - INFO - Vector DB search completed in 5.24s
[ 2025-08-11 15:38:11,816 ] 52 root - INFO - Results relevant: True
[ 2025-08-11 15:38:11,819 ] 68 root - INFO - \u2705 Finished 'search_documents' in 5.2439 seconds.
[ 2025-08-11 15:38:11,823 ] 457 root - INFO - \u2705 Vector search: 5.28s
[ 2025-08-11 15:38:12,096 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-11 15:38:12,099 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG NLP technical documentation...'
[ 2025-08-11 15:38:12,404 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:38:12,407 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 15:38:12,835 ] 59 root - INFO - \u26a1 Web search: 2.32s, 2 results
[ 2025-08-11 15:38:12,835 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3210 seconds.
[ 2025-08-11 15:38:12,836 ] 525 root - INFO - \U0001f310 Web search: 2.32s, 2 results
[ 2025-08-11 15:38:13,235 ] 513 root - INFO - Enhanced web search query: premise of the year 2025 as of 2025 as of 2025
[ 2025-08-11 15:38:13,236 ] 513 root - INFO - Enhanced web search query: premise of the year 2025 as of 2025 as of 2025
[ 2025-08-11 15:38:13,237 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:13,238 ] 45 root - INFO - \U0001f310 Fast web search: 'premise of the year 2025 as of...'
[ 2025-08-11 15:38:13,240 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:13,243 ] 45 root - INFO - \U0001f310 Fast web search: 'premise of the year 2025 as of...'
[ 2025-08-11 15:38:13,310 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-11 15:38:13,310 ] 159 root - INFO - \u26a1 New response generated in 1.21s
[ 2025-08-11 15:38:13,311 ] 51 root - INFO - Vector DB search completed in 1.21s
[ 2025-08-11 15:38:13,311 ] 52 root - INFO - Results relevant: True
[ 2025-08-11 15:38:13,311 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.2146 seconds.
[ 2025-08-11 15:38:13,311 ] 457 root - INFO - \u2705 Vector search: 1.22s
[ 2025-08-11 15:38:15,526 ] 59 root - INFO - \u26a1 Web search: 2.28s, 2 results
[ 2025-08-11 15:38:15,531 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.2899 seconds.
[ 2025-08-11 15:38:15,532 ] 525 root - INFO - \U0001f310 Web search: 2.30s, 2 results
[ 2025-08-11 15:38:16,125 ] 59 root - INFO - \u26a1 Web search: 2.89s, 2 results
[ 2025-08-11 15:38:16,128 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8908 seconds.
[ 2025-08-11 15:38:16,132 ] 525 root - INFO - \U0001f310 Web search: 2.90s, 2 results
[ 2025-08-11 15:38:20,149 ] 513 root - INFO - Enhanced web search query: current weather as of 2025 as of 2025
[ 2025-08-11 15:38:20,153 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:20,153 ] 45 root - INFO - \U0001f310 Fast web search: 'current weather as of 2025 as ...'
[ 2025-08-11 15:38:20,456 ] 513 root - INFO - Enhanced web search query: premise of the year 2025 meaning as of 2025 as of 2025
[ 2025-08-11 15:38:20,457 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:20,458 ] 45 root - INFO - \U0001f310 Fast web search: 'premise of the year 2025 meani...'
[ 2025-08-11 15:38:23,285 ] 59 root - INFO - \u26a1 Web search: 2.83s, 2 results
[ 2025-08-11 15:38:23,286 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8305 seconds.
[ 2025-08-11 15:38:23,287 ] 525 root - INFO - \U0001f310 Web search: 2.83s, 2 results
[ 2025-08-11 15:38:23,370 ] 59 root - INFO - \u26a1 Web search: 3.22s, 2 results
[ 2025-08-11 15:38:23,371 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.2178 seconds.
[ 2025-08-11 15:38:23,373 ] 525 root - INFO - \U0001f310 Web search: 3.22s, 2 results
[ 2025-08-11 15:38:23,780 ] 465 livekit.agents - WARNING - rotate_segment called while previous segment is still being rotated
[ 2025-08-11 15:38:23,785 ] 513 root - INFO - Enhanced web search query: Chief Minister queries as of 2025 as of 2025
[ 2025-08-11 15:38:23,787 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-11 15:38:23,788 ] 45 root - INFO - \U0001f310 Fast web search: 'Chief Minister queries as of 2...'
[ 2025-08-11 15:38:26,695 ] 59 root - INFO - \u26a1 Web search: 2.91s, 2 results
[ 2025-08-11 15:38:26,696 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9094 seconds.
[ 2025-08-11 15:38:26,697 ] 525 root - INFO - \U0001f310 Web search: 2.91s, 2 results
[ 2025-08-11 15:38:26,701 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-11 15:38:34,987 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-11 15:38:36,512 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-11 15:38:37,478 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-11 15:38:38,575 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-11 15:38:38,578 ] 37 root - INFO - \U0001f50d Vector DB search for: 'NKT cables operational voltages...'
[ 2025-08-11 15:38:39,297 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 15:38:39,783 ] 107 livekit.agents - WARNING - exiting forcefully
