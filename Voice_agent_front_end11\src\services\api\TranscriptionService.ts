// TranscriptionService: Real-time transcription API integration
export interface LiveTranscription {
  filename: string;
  recent_content: string;
  full_content: string;
  last_modified: string;
}

export interface TranscriptionResponse {
  status: string;
  transcription: LiveTranscription | null;
}

export class TranscriptionService {
  private baseUrl: string;
  private pollingInterval: NodeJS.Timeout | null = null;
  private subscribers: Set<(transcription: LiveTranscription | null) => void> = new Set();
  private lastTranscription: LiveTranscription | null = null;
  private lastFetch: number = 0;
  private minFetchInterval: number = 1000; // Minimum 1 second between API calls for faster updates
  private consecutiveErrors: number = 0;
  private maxConsecutiveErrors: number = 3;

  constructor(baseUrl: string = 'http://localhost:8000') {
    this.baseUrl = baseUrl;
  }

  /**
   * Fetch the latest live transcription from the backend (optimized)
   */
  async fetchLiveTranscription(): Promise<LiveTranscription | null> {
    const now = Date.now();
    
    // Skip fetch if too soon since last fetch
    if (now - this.lastFetch < this.minFetchInterval) {
      return this.lastTranscription;
    }

    // Skip fetch if too many consecutive errors
    if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
      return this.lastTranscription;
    }

    try {
      this.lastFetch = now;
      
      const response = await fetch(`${this.baseUrl}/api/live-transcription`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: TranscriptionResponse = await response.json();
      
      if (data.status === 'success') {
        const newTranscription = data.transcription;
        
        // Only update if content has actually changed
        if (!this.lastTranscription || 
            !newTranscription ||
            this.lastTranscription.last_modified !== newTranscription.last_modified ||
            this.lastTranscription.recent_content !== newTranscription.recent_content) {
          
          this.lastTranscription = newTranscription;
          this.consecutiveErrors = 0; // Reset error count on success
        }
        
        return this.lastTranscription;
      }
      
      return this.lastTranscription;
    } catch (error) {
      this.consecutiveErrors++;
      console.error(`Error fetching live transcription (${this.consecutiveErrors}/${this.maxConsecutiveErrors}):`, error);
      
      // Return cached data on error
      return this.lastTranscription;
    }
  }

  /**
   * Subscribe to live transcription updates
   */
  subscribe(callback: (transcription: LiveTranscription | null) => void): () => void {
    this.subscribers.add(callback);
    
    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);
      if (this.subscribers.size === 0) {
        this.stopPolling();
      }
    };
  }

  /**
   * Start polling for live transcription updates (optimized for real-time)
   */
  startPolling(intervalMs: number = 2000): void { // Faster default interval
    if (this.pollingInterval) {
      return; // Already polling
    }

    const poll = async () => {
      const transcription = await this.fetchLiveTranscription();
      this.notifySubscribers(transcription);
    };

    // Initial fetch
    poll();

    // Set up polling interval with adaptive timing
    this.pollingInterval = setInterval(poll, Math.max(intervalMs, this.minFetchInterval));
  }

  /**
   * Stop polling for updates
   */
  stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
  }

  /**
   * Notify all subscribers of new transcription data
   */
  private notifySubscribers(transcription: LiveTranscription | null): void {
    this.subscribers.forEach(callback => {
      try {
        callback(transcription);
      } catch (error) {
        console.error('Error in transcription subscriber callback:', error);
      }
    });
  }

  /**
   * Get all recent transcriptions
   */
  async fetchRecentTranscriptions(): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/transcriptions`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.transcriptions || [];
      }
      
      return [];
    } catch (error) {
      console.error('Error fetching recent transcriptions:', error);
      return [];
    }
  }

  /**
   * Check if backend is available
   */
  async checkBackendHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.ok;
    } catch (error) {
      console.error('Backend health check failed:', error);
      return false;
    }
  }

  /**
   * Destroy the service and clean up resources
   */
  destroy(): void {
    this.stopPolling();
    this.subscribers.clear();
  }
}

// Singleton instance
export const transcriptionService = new TranscriptionService();