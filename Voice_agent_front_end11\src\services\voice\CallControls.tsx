import React from 'react';

interface CallControlsProps {
  isActive: boolean;
  isMuted: boolean;
  isCallActive: boolean;
  onStartStop: () => void;
  onMuteToggle: () => void;
  onCallToggle: () => void;
  onTransfer: () => void;
  onMessage: () => void;
  onSettings: () => void;
  className?: string;
}

export const CallControls: React.FC<CallControlsProps> = ({
  isActive,
  isMuted,
  isCallActive,
  onStartStop,
  onMuteToggle,
  onCallToggle,
  onTransfer,
  onMessage,
  onSettings,
  className = '',
}) => {
  return (
    <div className={`flex items-center space-x-4 ${className}`}>
      <button onClick={onStartStop} className="px-3 py-1 rounded bg-green-600 text-white">
        {isActive ? 'Stop' : 'Start'}
      </button>
      <button onClick={onMuteToggle} className="px-3 py-1 rounded bg-yellow-600 text-white">
        {isMuted ? 'Unmute' : 'Mute'}
      </button>
      <button onClick={onCallToggle} className="px-3 py-1 rounded bg-blue-600 text-white">
        {isCallActive ? 'End Call' : 'Start Call'}
      </button>
      <button onClick={onTransfer} className="px-3 py-1 rounded bg-purple-600 text-white">
        Transfer
      </button>
      <button onClick={onMessage} className="px-3 py-1 rounded bg-gray-600 text-white">
        Message
      </button>
      <button onClick={onSettings} className="px-3 py-1 rounded bg-gray-800 text-white">
        Settings
      </button>
    </div>
  );
}; 