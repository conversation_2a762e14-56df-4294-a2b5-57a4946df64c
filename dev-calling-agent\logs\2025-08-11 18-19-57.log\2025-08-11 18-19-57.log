[ 2025-08-11 18:20:00,811 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-11 18:20:00,856 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,856 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,857 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,857 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,857 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,858 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,858 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,858 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,859 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,859 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,862 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,865 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:00,879 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,880 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,880 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,880 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,882 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,882 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,882 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,882 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,883 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,883 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,883 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:00,883 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:01,300 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-11 18:20:04,994 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 18:20:05,084 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-11 18:20:05,160 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:20:05,160 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:20:05,554 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4700 seconds.
[ 2025-08-11 18:20:09,651 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-11 18:20:18,224 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-11 18:20:18,225 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-11 18:20:18,225 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-11 18:20:18,875 ] 19 livekit.agents - WARNING - Running <Task pending name='job_user_entrypoint' coro=<_JobProc._run_job_task.<locals>._traceable_entrypoint() running at D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py:71> wait_for=<Future pending cb=[Task.task_wakeup()]> cb=[_JobProc._run_job_task.<locals>.<lambda>() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:258, _JobProc._run_job_task.<locals>.log_exception() at D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py:260]> took too long: 13.80 seconds
[ 2025-08-11 18:20:32,871 ] 375 root - INFO - \U0001f3af Initial language prompt sent
[ 2025-08-11 18:20:32,968 ] 390 root - ERROR - Session monitoring error: 'Room' object has no attribute 'participants'
[ 2025-08-11 18:20:37,152 ] 307 root - INFO - \U0001f4f1 DTMF received: 1
[ 2025-08-11 18:20:37,153 ] 166 root - INFO - \U0001f30d Language instructions updated to: English
[ 2025-08-11 18:20:37,154 ] 220 root - INFO - \U0001f4dd Language instructions updated in session
[ 2025-08-11 18:20:37,154 ] 62 livekit - ERROR - failed to emit event sip_dtmf_received
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\rtc\event_emitter.py", line 58, in emit
    callback(*callback_args)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 321, in handle_dtmf
    session.ensure_message_processing()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedCallSession' object has no attribute 'ensure_message_processing'
[ 2025-08-11 18:20:47,820 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 18:21:09,120 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-11 18:21:36,394 ] 855 livekit.agents - INFO - received job request
[ 2025-08-11 18:21:36,565 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-11 18:21:36,567 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-11 18:21:53,940 ] 375 root - INFO - \U0001f3af Initial language prompt sent
[ 2025-08-11 18:21:54,036 ] 390 root - ERROR - Session monitoring error: 'Room' object has no attribute 'participants'
[ 2025-08-11 18:21:55,580 ] 307 root - INFO - \U0001f4f1 DTMF received: 1
[ 2025-08-11 18:21:55,581 ] 166 root - INFO - \U0001f30d Language instructions updated to: English
[ 2025-08-11 18:21:55,581 ] 220 root - INFO - \U0001f4dd Language instructions updated in session
[ 2025-08-11 18:21:55,581 ] 62 livekit - ERROR - failed to emit event sip_dtmf_received
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\rtc\event_emitter.py", line 58, in emit
    callback(*callback_args)
  File "D:\integrate_real_frontend\dev-calling-agent\unified_agent.py", line 321, in handle_dtmf
    session.ensure_message_processing()
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EnhancedCallSession' object has no attribute 'ensure_message_processing'
[ 2025-08-11 18:22:01,872 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-11 18:22:03,975 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-11 18:22:04,354 ] 107 livekit.agents - WARNING - exiting forcefully
