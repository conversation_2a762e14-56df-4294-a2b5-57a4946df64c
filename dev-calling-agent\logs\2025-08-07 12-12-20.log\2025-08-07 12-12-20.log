[ 2025-08-07 12:12:23,991 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 12:12:23,991 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 12:12:24,361 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3703 seconds.
[ 2025-08-07 12:12:28,096 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 12:12:36,870 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:12:36,871 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:12:36,871 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 12:12:37,580 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:12:37,581 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:12:37,581 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 12:12:37,582 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 12:12:37,588 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 12:12:37,656 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,658 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,658 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,659 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,660 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,661 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,661 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,662 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,663 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,663 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,664 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,664 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:12:37,824 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,824 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,825 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,826 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,826 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,826 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:37,826 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:12:38,293 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 12:13:03,294 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:13:03,663 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:13:03,669 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:13:05,573 ] 666 root - INFO - Call logging started for call ID: SCL_YEkGnCp47CYg
[ 2025-08-07 12:13:11,038 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:13:11,043 ] 755 root - INFO - Call logging ended for call ID: SCL_YEkGnCp47CYg
[ 2025-08-07 12:13:11,043 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:13:40,816 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 12:13:40,817 ] 207 root - INFO - \u26a1 Language detection: 1.250s
[ 2025-08-07 12:13:40,818 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 12:13:40,819 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:13:40,820 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage...'
[ 2025-08-07 12:13:44,219 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:13:44,221 ] 159 root - INFO - \u26a1 New response generated in 3.40s
[ 2025-08-07 12:13:44,222 ] 51 root - INFO - Vector DB search completed in 3.40s
[ 2025-08-07 12:13:44,223 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:13:44,224 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.4049 seconds.
[ 2025-08-07 12:13:44,225 ] 457 root - INFO - \u2705 Vector search: 4.66s
[ 2025-08-07 12:13:44,840 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:13:44,842 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage range...'
[ 2025-08-07 12:13:45,917 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:13:45,918 ] 159 root - INFO - \u26a1 New response generated in 1.08s
[ 2025-08-07 12:13:45,918 ] 51 root - INFO - Vector DB search completed in 1.08s
[ 2025-08-07 12:13:45,919 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:13:45,919 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0782 seconds.
[ 2025-08-07 12:13:45,921 ] 457 root - INFO - \u2705 Vector search: 1.08s
[ 2025-08-07 12:13:46,438 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:13:46,439 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage range for ...'
[ 2025-08-07 12:13:47,526 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:13:47,526 ] 159 root - INFO - \u26a1 New response generated in 1.09s
[ 2025-08-07 12:13:47,528 ] 51 root - INFO - Vector DB search completed in 1.09s
[ 2025-08-07 12:13:47,528 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:13:47,528 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0905 seconds.
[ 2025-08-07 12:13:47,529 ] 457 root - INFO - \u2705 Vector search: 1.09s
[ 2025-08-07 12:13:47,991 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 12:13:47,992 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage range for ...'
[ 2025-08-07 12:13:49,157 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 12:13:49,158 ] 159 root - INFO - \u26a1 New response generated in 1.17s
[ 2025-08-07 12:13:49,159 ] 51 root - INFO - Vector DB search completed in 1.17s
[ 2025-08-07 12:13:49,159 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 12:13:49,160 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.1688 seconds.
[ 2025-08-07 12:13:49,161 ] 457 root - INFO - \u2705 Vector search: 1.17s
[ 2025-08-07 12:13:49,163 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-07 12:14:27,114 ] 513 root - INFO - Enhanced web search query: current five ministers of India as of 2025 as of 2025
[ 2025-08-07 12:14:27,117 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:14:27,118 ] 45 root - INFO - \U0001f310 Fast web search: 'current five ministers of Indi...'
[ 2025-08-07 12:14:30,528 ] 59 root - INFO - \u26a1 Web search: 3.41s, 2 results
[ 2025-08-07 12:14:30,528 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.4113 seconds.
[ 2025-08-07 12:14:30,529 ] 525 root - INFO - \U0001f310 Web search: 3.41s, 2 results
[ 2025-08-07 12:14:31,063 ] 513 root - INFO - Enhanced web search query: list of current five ministers of India as of 2025 as of 2025
[ 2025-08-07 12:14:31,066 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:14:31,067 ] 45 root - INFO - \U0001f310 Fast web search: 'list of current five ministers...'
[ 2025-08-07 12:14:33,908 ] 59 root - INFO - \u26a1 Web search: 2.84s, 2 results
[ 2025-08-07 12:14:33,909 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8434 seconds.
[ 2025-08-07 12:14:33,910 ] 525 root - INFO - \U0001f310 Web search: 2.85s, 2 results
[ 2025-08-07 12:14:34,544 ] 513 root - INFO - Enhanced web search query: list of current five ministers of India as of 2025 as of 2025
[ 2025-08-07 12:14:34,545 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:14:34,546 ] 45 root - INFO - \U0001f310 Fast web search: 'list of current five ministers...'
[ 2025-08-07 12:14:37,176 ] 59 root - INFO - \u26a1 Web search: 2.63s, 2 results
[ 2025-08-07 12:14:37,177 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6319 seconds.
[ 2025-08-07 12:14:37,178 ] 525 root - INFO - \U0001f310 Web search: 2.63s, 2 results
[ 2025-08-07 12:14:38,007 ] 513 root - INFO - Enhanced web search query: list of current five ministers of India as of 2025 as of 2025
[ 2025-08-07 12:14:38,009 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:14:38,009 ] 45 root - INFO - \U0001f310 Fast web search: 'list of current five ministers...'
[ 2025-08-07 12:14:40,613 ] 59 root - INFO - \u26a1 Web search: 2.60s, 2 results
[ 2025-08-07 12:14:40,615 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6057 seconds.
[ 2025-08-07 12:14:40,616 ] 525 root - INFO - \U0001f310 Web search: 2.61s, 2 results
[ 2025-08-07 12:14:40,618 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-07 12:14:57,339 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:14:57,342 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:14:57,342 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:14:59,841 ] 59 root - INFO - \u26a1 Web search: 2.50s, 2 results
[ 2025-08-07 12:14:59,841 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4988 seconds.
[ 2025-08-07 12:14:59,842 ] 525 root - INFO - \U0001f310 Web search: 2.50s, 2 results
[ 2025-08-07 12:15:00,906 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:15:00,908 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:15:00,909 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:15:03,539 ] 59 root - INFO - \u26a1 Web search: 2.63s, 2 results
[ 2025-08-07 12:15:03,540 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6328 seconds.
[ 2025-08-07 12:15:03,540 ] 525 root - INFO - \U0001f310 Web search: 2.63s, 2 results
[ 2025-08-07 12:15:04,635 ] 513 root - INFO - Enhanced web search query: current Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:15:04,638 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:15:04,638 ] 45 root - INFO - \U0001f310 Fast web search: 'current Prime Minister of Indi...'
[ 2025-08-07 12:15:06,692 ] 59 root - INFO - \u26a1 Web search: 2.05s, 2 results
[ 2025-08-07 12:15:06,692 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.0537 seconds.
[ 2025-08-07 12:15:06,693 ] 525 root - INFO - \U0001f310 Web search: 2.06s, 2 results
[ 2025-08-07 12:15:21,107 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:15:44,162 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:15:57,719 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:15:59,079 ] 666 root - INFO - Call logging started for call ID: SCL_he6k2i262n2H
[ 2025-08-07 12:16:02,839 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:16:02,843 ] 755 root - INFO - Call logging ended for call ID: SCL_he6k2i262n2H
[ 2025-08-07 12:16:02,844 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:16:05,940 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:16:08,469 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:16:11,739 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:16:13,275 ] 666 root - INFO - Call logging started for call ID: SCL_YMVxXWe5gHDY
[ 2025-08-07 12:16:17,761 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-08-07 12:16:18,936 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:16:18,955 ] 755 root - INFO - Call logging ended for call ID: SCL_YMVxXWe5gHDY
[ 2025-08-07 12:16:18,958 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:16:31,609 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 12:16:31,611 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 12:16:31,612 ] 513 root - INFO - Enhanced web search query: Andhra Pradesh Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:16:31,616 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:16:31,618 ] 45 root - INFO - \U0001f310 Fast web search: 'Andhra Pradesh Chief Minister ...'
[ 2025-08-07 12:16:34,363 ] 59 root - INFO - \u26a1 Web search: 2.75s, 2 results
[ 2025-08-07 12:16:34,367 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7514 seconds.
[ 2025-08-07 12:16:34,375 ] 525 root - INFO - \U0001f310 Web search: 2.78s, 2 results
[ 2025-08-07 12:16:34,802 ] 513 root - INFO - Enhanced web search query: Andhra Pradesh Chief Minister as of 2025 as of 2025
[ 2025-08-07 12:16:34,804 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:16:34,805 ] 45 root - INFO - \U0001f310 Fast web search: 'Andhra Pradesh Chief Minister ...'
[ 2025-08-07 12:16:37,587 ] 59 root - INFO - \u26a1 Web search: 2.78s, 2 results
[ 2025-08-07 12:16:37,590 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7860 seconds.
[ 2025-08-07 12:16:37,591 ] 525 root - INFO - \U0001f310 Web search: 2.79s, 2 results
[ 2025-08-07 12:16:46,238 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:16:47,084 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:16:48,766 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:16:53,766 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:16:58,798 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:17:04,510 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:17:04,512 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:17:04,516 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-07 12:17:07,521 ] 59 root - INFO - \u26a1 Web search: 3.01s, 2 results
[ 2025-08-07 12:17:07,522 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.0106 seconds.
[ 2025-08-07 12:17:07,524 ] 525 root - INFO - \U0001f310 Web search: 3.01s, 2 results
[ 2025-08-07 12:17:07,811 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:17:07,813 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:17:07,813 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-07 12:17:07,990 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:17:10,530 ] 59 root - INFO - \u26a1 Web search: 2.72s, 2 results
[ 2025-08-07 12:17:10,531 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7174 seconds.
[ 2025-08-07 12:17:10,533 ] 525 root - INFO - \U0001f310 Web search: 2.72s, 2 results
[ 2025-08-07 12:17:16,364 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 12:17:16,364 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 12:17:16,369 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-07 12:17:17,277 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:17:17,775 ] 1116 livekit.agents - WARNING - skipping user input, speech scheduling is paused
[ 2025-08-07 12:17:18,721 ] 59 root - INFO - \u26a1 Web search: 2.35s, 2 results
[ 2025-08-07 12:17:18,721 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3575 seconds.
[ 2025-08-07 12:17:18,722 ] 525 root - INFO - \U0001f310 Web search: 2.36s, 2 results
[ 2025-08-07 12:17:31,403 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:17:36,401 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:17:41,223 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:17:41,405 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:17:41,630 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:17:41,634 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:17:41,638 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:17:41,641 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:17:43,921 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 12:18:14,050 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 12:18:15,044 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 12:18:15,046 ] 560 livekit.agents - INFO - shutting down worker
