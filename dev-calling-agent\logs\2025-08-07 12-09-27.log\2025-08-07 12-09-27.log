[ 2025-08-07 12:09:30,131 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': 'gsk_hsVMQamCz3PqwPFaPQ9nW Gdyb3FYscY5gMWaQrmveCeLVud G6qkJ'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 12:09:30,131 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 12:09:30,502 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.3715 seconds.
[ 2025-08-07 12:09:34,175 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 12:09:43,876 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:09:43,876 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:09:43,876 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 12:09:44,653 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 12:09:44,653 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 12:09:44,653 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 12:09:44,655 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 12:09:44,669 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 12:09:44,731 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,734 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,735 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,736 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,737 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,738 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,738 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,739 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,740 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,741 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,741 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,742 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:44,921 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,922 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,922 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,922 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,923 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,923 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,923 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,923 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,923 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,924 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,924 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:44,924 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:45,737 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 12:09:52,495 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 12:09:52,681 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 12:09:52,684 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 12:09:53,719 ] 666 root - INFO - Call logging started for call ID: SCL_yjhViwVdZZ7X
[ 2025-08-07 12:09:54,873 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m3xekfgd9h94nmcmas5ee, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:09:55,098 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m3xnrf7hbeaz5srt02nex, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:09:57,228 ] 189 livekit.agents - WARNING - failed to generate LLM completion, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\llm\llm.py", line 174, in _main_task
    return await self._run()
           ^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\llm.py", line 691, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m3zr5fgdrcwj9nyz7d9ja, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=True)
[ 2025-08-07 12:09:57,951 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 12:09:57,959 ] 755 root - INFO - Call logging ended for call ID: SCL_yjhViwVdZZ7X
[ 2025-08-07 12:09:57,960 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 12:09:58,541 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m4116fgerxh1s57dn2cy8, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:09:59,081 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m41j7fgfb09erkr4sv19h, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:10:01,379 ] 145 livekit.agents - WARNING - failed to recognize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m43syfada5per2qw6ha7j, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:10:03,580 ] 877 livekit.agents - ERROR - AgentSession is closing due to unrecoverable error
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\stt\stt.py", line 118, in recognize
    event = await self._recognize_impl(
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\openai\stt.py", line 397, in _recognize_impl
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Error code: 401 - {'error': {'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}} (status_code=401, request_id=req_01k21m45ypfgjthk9hh19egzct, body={'message': 'Invalid API Key', 'type': 'invalid_request_error', 'code': 'invalid_api_key'}, retryable=False)
[ 2025-08-07 12:10:07,180 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 12:10:27,960 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 12:12:16,434 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 12:12:16,435 ] 560 livekit.agents - INFO - shutting down worker
