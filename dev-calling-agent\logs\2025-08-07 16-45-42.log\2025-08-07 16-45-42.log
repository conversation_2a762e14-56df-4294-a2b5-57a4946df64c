[ 2025-08-07 16:45:48,540 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 16:45:48,540 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 16:45:49,138 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.5971 seconds.
[ 2025-08-07 16:45:55,005 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 16:46:06,606 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:46:06,606 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:46:06,607 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 16:46:07,757 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:46:07,757 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:46:07,757 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 16:46:07,758 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 16:46:07,764 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 16:46:07,776 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,776 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,778 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,779 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,780 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,780 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,781 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,784 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,787 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,788 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,788 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:07,790 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:08,008 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,008 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,010 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,010 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,011 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,011 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,011 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,011 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,012 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,012 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,012 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,012 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:46:08,773 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 16:46:09,167 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:46:09,323 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:46:09,324 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:47:28,735 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:47:30,292 ] 666 root - INFO - Call logging started for call ID: SCL_d6xq7KDhpJPR
[ 2025-08-07 16:47:35,289 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:35,835 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:37,886 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:47:37,896 ] 755 root - INFO - Call logging ended for call ID: SCL_d6xq7KDhpJPR
[ 2025-08-07 16:47:37,898 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:47:40,513 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:41,075 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:46,815 ] 180 root - INFO - \u26a1 Langdetect: el
[ 2025-08-07 16:47:46,818 ] 259 root - WARNING - Unsupported language code: el. Defaulting to English.
[ 2025-08-07 16:47:46,820 ] 207 root - INFO - \u26a1 Language detection: 0.870s
[ 2025-08-07 16:47:46,821 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 16:47:46,823 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 16:47:46,823 ] 37 root - INFO - \U0001f50d Vector DB search for: '\u03c0\u03bf\u03c5 \u03ae\u03c1\u03b8\u03b5 \u03b7 \u03b1\u03bc\u03b1\u03b8\u03b7\u03bd\u03af\u03b1...'
[ 2025-08-07 16:47:47,905 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:48,451 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:51,042 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:51,973 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 16:47:51,975 ] 159 root - INFO - \u26a1 New response generated in 5.15s
[ 2025-08-07 16:47:51,975 ] 51 root - INFO - Vector DB search completed in 5.15s
[ 2025-08-07 16:47:51,978 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 16:47:51,981 ] 68 root - INFO - \u2705 Finished 'search_documents' in 5.1576 seconds.
[ 2025-08-07 16:47:51,985 ] 457 root - INFO - \u2705 Vector search: 6.03s
[ 2025-08-07 16:47:53,392 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:54,367 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:54,863 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:57,249 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:47:59,693 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:48:03,838 ] 513 root - INFO - Enhanced web search query: PM of India as of 2025 as of 2025
[ 2025-08-07 16:48:03,839 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 16:48:03,842 ] 45 root - INFO - \U0001f310 Fast web search: 'PM of India as of 2025 as of 2...'
[ 2025-08-07 16:48:06,482 ] 59 root - INFO - \u26a1 Web search: 2.64s, 2 results
[ 2025-08-07 16:48:06,483 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6435 seconds.
[ 2025-08-07 16:48:06,484 ] 525 root - INFO - \U0001f310 Web search: 2.65s, 2 results
[ 2025-08-07 16:48:10,243 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:48:10,779 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:48:11,146 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:48:31,985 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:50:08,569 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:50:08,812 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:50:08,813 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:50:50,822 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:50:51,245 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:50:51,249 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:50:53,340 ] 666 root - INFO - Call logging started for call ID: SCL_k8c4yG5zhfKj
[ 2025-08-07 16:50:56,350 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:50:56,881 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:50:58,825 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:50:58,835 ] 755 root - INFO - Call logging ended for call ID: SCL_k8c4yG5zhfKj
[ 2025-08-07 16:50:58,837 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:51:00,611 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:01,103 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:03,510 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:05,907 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:17,412 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:17,893 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:20,308 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:22,697 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:40,663 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 16:51:40,670 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 16:51:40,672 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 16:51:40,677 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 16:51:40,681 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-07 16:51:43,293 ] 59 root - INFO - \u26a1 Web search: 2.61s, 2 results
[ 2025-08-07 16:51:43,293 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6164 seconds.
[ 2025-08-07 16:51:43,294 ] 525 root - INFO - \U0001f310 Web search: 2.64s, 2 results
[ 2025-08-07 16:51:48,491 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:49,026 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:51,429 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 2.0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:53,923 ] 21 livekit.agents - ERROR - Error in _tts_inference_task
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\log.py", line 16, in async_fn_logs
    return await fn(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\generation.py", line 212, in _tts_inference_task
    async for audio_frame in tts_node:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\voice\agent.py", line 408, in tts_node
    async for ev in stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 520, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 348, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 118, in _run
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\stream_adapter.py", line 108, in _synthesize
    async for audio in tts_stream:
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 285, in __anext__
    raise exc  # noqa: B904
    ^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 228, in _main_task
    await self._run(output_emitter)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\plugins\speechify\tts.py", line 248, in _run
    raise APIStatusError(
livekit.agents._exceptions.APIStatusError: Unauthorized (status_code=401, request_id=None, body=None, retryable=False)
[ 2025-08-07 16:51:58,155 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:52:42,690 ] 932 livekit.agents - INFO - worker is at full capacity, marking as unavailable
[ 2025-08-07 16:52:45,207 ] 937 livekit.agents - INFO - worker is below capacity, marking as available
[ 2025-08-07 16:52:59,465 ] 685 livekit.agents - WARNING - failed to connect to livekit, retrying in 0s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 672, in _connection_task
    await self._run_ws(ws)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 753, in _run_ws
    await asyncio.gather(*tasks)
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\worker.py", line 725, in _recv_task
    raise Exception("worker connection closed unexpectedly")
Exception: worker connection closed unexpectedly
[ 2025-08-07 16:53:00,452 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 16:53:10,542 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 16:53:10,543 ] 560 livekit.agents - INFO - shutting down worker
