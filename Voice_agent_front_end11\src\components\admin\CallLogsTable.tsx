
import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Download, Filter, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { callLogsApi, ParsedCallLog } from '@/services/callLogsApi';

export const CallLogsTable = () => {
  const [callLogs, setCallLogs] = useState<ParsedCallLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Fetch call logs from API
  const fetchCallLogs = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // First check if backend is available
      const isBackendHealthy = await callLogsApi.healthCheck();
      if (!isBackendHealthy) {
        throw new Error('Backend server is not responding. Please start the backend server by running: python app.py');
      }
      
      const logs = await callLogsApi.getCallLogs();
      setCallLogs(logs);
      setLastRefresh(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch call logs');
      console.error('Error fetching call logs:', err);
    } finally {
      setLoading(false);
    }
  };

  // Fetch logs on component mount
  useEffect(() => {
    fetchCallLogs();
  }, []);

  // Handle export functionality
  const handleExport = async () => {
    try {
      await callLogsApi.exportCallLogs();
      // Could implement actual file download here
    } catch (err) {
      console.error('Export failed:', err);
    }
  };

  // Handle view log detail
  const handleViewDetail = async (filename: string) => {
    try {
      const detail = await callLogsApi.getCallLogDetail(filename);
      // Could open a modal or navigate to detail view
      console.log('Call log detail:', detail);
    } catch (err) {
      console.error('Failed to fetch log detail:', err);
    }
  };

  return (
    <Card className="p-6 bg-gray-800/50 border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-xl font-semibold text-white">Recent Call Logs</h3>
          {!loading && !error && (
            <p className="text-sm text-gray-400 mt-1">
              Last updated: {lastRefresh.toLocaleTimeString()} • {callLogs.length} logs
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchCallLogs}
            disabled={loading}
            className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" size="sm" className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500">
            <Filter className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleExport}
            className="text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700 hover:text-white hover:border-gray-500"
          >
            <Download className="w-4 h-4 mr-2" />
            Export Logs
          </Button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-purple-400 mr-3" />
          <span className="text-gray-300">Loading call logs...</span>
        </div>
      )}

      {/* Error State */}
      {error && !loading && (
        <div className="flex items-center justify-center py-12">
          <AlertCircle className="w-8 h-8 text-red-400 mr-3" />
          <div className="text-center">
            <p className="text-red-400 font-medium">Failed to load call logs</p>
            <p className="text-gray-400 text-sm mt-1">{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={fetchCallLogs}
              className="mt-3 text-white border-gray-600 bg-gray-800/50 hover:bg-gray-700"
            >
              Try Again
            </Button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && callLogs.length === 0 && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <Eye className="w-12 h-12 text-gray-500 mx-auto mb-4 opacity-50" />
            <p className="text-gray-400 font-medium">No call logs found</p>
            <p className="text-gray-500 text-sm mt-1">Call logs will appear here once calls are made</p>
          </div>
        </div>
      )}

      {/* Data Table */}
      {!loading && !error && callLogs.length > 0 && (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="border-gray-700 hover:bg-transparent">
                <TableHead className="text-gray-300">Call ID</TableHead>
                <TableHead className="text-gray-300">Timestamp</TableHead>
                <TableHead className="text-gray-300">Duration</TableHead>
                <TableHead className="text-gray-300">Language</TableHead>
                <TableHead className="text-gray-300">Status</TableHead>
                <TableHead className="text-gray-300">AI Confidence</TableHead>
                <TableHead className="text-gray-300">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {callLogs.map((log) => (
                <TableRow key={log.id} className="border-gray-800 hover:bg-gray-900/30">
                  <TableCell className="text-white font-mono">{log.id}</TableCell>
                  <TableCell className="text-gray-300">{log.timestamp}</TableCell>
                  <TableCell className="text-gray-300">{log.duration}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className="text-blue-400 border-blue-400">
                      {log.language}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={log.transferred ? "secondary" : "default"}
                      className={
                        log.status === 'Transferred' || log.transferred
                          ? "bg-yellow-500/20 text-yellow-400 border-yellow-400"
                          : log.status === 'Completed'
                          ? "bg-green-500/20 text-green-400 border-green-400"
                          : log.status === 'In Progress'
                          ? "bg-blue-500/20 text-blue-400 border-blue-400"
                          : "bg-gray-500/20 text-gray-400 border-gray-400"
                      }
                    >
                      {log.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-gray-300">{log.aiConfidence}</TableCell>
                  <TableCell>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => handleViewDetail(log.filename)}
                      className="text-gray-400 hover:text-white hover:bg-gray-700"
                      title="View call details"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </Card>
  );
};
