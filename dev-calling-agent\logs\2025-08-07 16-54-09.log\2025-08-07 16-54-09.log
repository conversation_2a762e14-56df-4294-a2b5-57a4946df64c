[ 2025-08-07 16:54:12,746 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'HwGQf9BXnooaNMX680n7zPW_bxVL7Tw7bBu_3gRxiho='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-07 16:54:12,746 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-07 16:54:13,149 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4032 seconds.
[ 2025-08-07 16:54:17,057 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-07 16:54:34,073 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:54:34,073 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:54:34,074 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-07 16:54:35,012 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-07 16:54:35,013 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-07 16:54:35,013 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-07 16:54:35,014 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-07 16:54:35,030 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-07 16:54:35,140 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,142 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,143 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,146 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,147 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,148 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,152 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,155 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,158 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,159 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,160 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,162 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:54:35,564 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,565 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,566 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,568 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,570 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,571 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,573 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,575 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,576 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,577 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,580 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:35,582 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:54:37,088 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-07 16:55:17,230 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:55:18,605 ] 666 root - INFO - Call logging started for call ID: SCL_Akhqmz5uBkSd
[ 2025-08-07 16:55:24,975 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:55:24,987 ] 755 root - INFO - Call logging ended for call ID: SCL_Akhqmz5uBkSd
[ 2025-08-07 16:55:24,994 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:55:44,448 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-07 16:55:44,450 ] 207 root - INFO - \u26a1 Language detection: 0.934s
[ 2025-08-07 16:55:44,451 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-07 16:55:44,452 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-07 16:55:44,453 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 16:55:44,455 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-07 16:55:47,024 ] 59 root - INFO - \u26a1 Web search: 2.57s, 2 results
[ 2025-08-07 16:55:47,027 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5740 seconds.
[ 2025-08-07 16:55:47,030 ] 525 root - INFO - \U0001f310 Web search: 3.51s, 2 results
[ 2025-08-07 16:55:47,927 ] 513 root - INFO - Enhanced web search query: Narendra Modi 2025 as of 2025
[ 2025-08-07 16:55:47,928 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 16:55:47,929 ] 45 root - INFO - \U0001f310 Fast web search: 'Narendra Modi 2025 as of 2025...'
[ 2025-08-07 16:55:50,570 ] 59 root - INFO - \u26a1 Web search: 2.64s, 2 results
[ 2025-08-07 16:55:50,573 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.6447 seconds.
[ 2025-08-07 16:55:50,578 ] 525 root - INFO - \U0001f310 Web search: 2.65s, 2 results
[ 2025-08-07 16:56:09,747 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 16:56:09,748 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage...'
[ 2025-08-07 16:56:13,698 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-07 16:56:13,700 ] 159 root - INFO - \u26a1 New response generated in 3.95s
[ 2025-08-07 16:56:13,703 ] 51 root - INFO - Vector DB search completed in 3.95s
[ 2025-08-07 16:56:13,710 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 16:56:13,712 ] 68 root - INFO - \u2705 Finished 'search_documents' in 3.9660 seconds.
[ 2025-08-07 16:56:13,714 ] 457 root - INFO - \u2705 Vector search: 3.97s
[ 2025-08-07 16:56:40,658 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-07 16:56:40,661 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear system operating voltage...'
[ 2025-08-07 16:56:40,662 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-07 16:56:40,663 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-07 16:56:40,664 ] 52 root - INFO - Results relevant: True
[ 2025-08-07 16:56:40,676 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0179 seconds.
[ 2025-08-07 16:56:40,679 ] 457 root - INFO - \u2705 Vector search: 0.02s
[ 2025-08-07 16:57:02,163 ] 513 root - INFO - Enhanced web search query: Kolkata weather as of 2025 as of 2025
[ 2025-08-07 16:57:02,163 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-07 16:57:02,166 ] 45 root - INFO - \U0001f310 Fast web search: 'Kolkata weather as of 2025 as ...'
[ 2025-08-07 16:57:05,349 ] 59 root - INFO - \u26a1 Web search: 3.18s, 2 results
[ 2025-08-07 16:57:05,350 ] 68 root - INFO - \u2705 Finished 'search_web' in 3.1865 seconds.
[ 2025-08-07 16:57:05,352 ] 525 root - INFO - \U0001f310 Web search: 3.19s, 2 results
[ 2025-08-07 16:57:21,043 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:57:43,582 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 16:57:44,143 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:57:44,146 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:59:16,900 ] 855 livekit.agents - INFO - received job request
[ 2025-08-07 16:59:17,068 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-07 16:59:17,071 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-07 16:59:18,608 ] 666 root - INFO - Call logging started for call ID: SCL_LncBTWnB7bX5
[ 2025-08-07 16:59:26,035 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-07 16:59:26,040 ] 755 root - INFO - Call logging ended for call ID: SCL_LncBTWnB7bX5
[ 2025-08-07 16:59:26,041 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraphagent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-07 16:59:30,244 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-07 16:59:53,619 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-07 17:00:20,931 ] 477 livekit.agents - INFO - draining worker
[ 2025-08-07 17:00:20,933 ] 560 livekit.agents - INFO - shutting down worker
