[ 2025-08-04 12:29:51,057 ] 34 root - INFO - Loaded model configuration: {'stt': {'model': 'whisper-large-v3', 'api_key': '********************************************************'}, 'llm': {'model': 'llama-3.1-8b-instant', 'api_key': '********************************************************'}, 'tts': {'model': 'simba-multilingual', 'api_key': 'xhxWGASqaStKrgNj5pk0KMkD0oeZ-udRLaQDle78Xqs='}, 'timestamp': '2025-08-03T09:38:37.098599'}
[ 2025-08-04 12:29:51,057 ] 62 root - INFO - \U0001f680 Starting '_load_model'...
[ 2025-08-04 12:29:51,494 ] 68 root - INFO - \u2705 Finished '_load_model' in 0.4365 seconds.
[ 2025-08-04 12:29:55,638 ] 227 sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: sentence-transformers/all-MiniLM-L6-v2
[ 2025-08-04 12:30:04,573 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 12:30:04,574 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 12:30:04,574 ] 24 root - INFO - ============================ Retriever Validator Invoke ============================== 
[ 2025-08-04 12:31:16,058 ] 101 root - INFO - Fast RAG chain ready
[ 2025-08-04 12:31:16,058 ] 84 root - INFO - Ultra-Fast SearchDocument initialized
[ 2025-08-04 12:31:16,059 ] 20 root - INFO - Vector Database Tool initialized successfully
[ 2025-08-04 12:31:16,059 ] 27 root - INFO - Web Search Tool initialized successfully
[ 2025-08-04 12:31:16,065 ] 374 livekit.agents - INFO - starting worker
[ 2025-08-04 12:31:16,132 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,134 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,136 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,140 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,141 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,143 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,145 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,148 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,149 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,150 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,151 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,152 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:16,170 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,171 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,171 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,171 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,173 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,173 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,173 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,173 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,173 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,174 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,174 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:16,174 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:17,560 ] 784 livekit.agents - INFO - registered worker
[ 2025-08-04 12:31:20,232 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 12:31:20,431 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 12:31:20,434 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 12:31:21,564 ] 666 root - INFO - Call logging started for call ID: SCL_4gATHm2ewqt6
[ 2025-08-04 12:31:24,141 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 12:31:24,146 ] 755 root - INFO - Call logging ended for call ID: SCL_4gATHm2ewqt6
[ 2025-08-04 12:31:24,147 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 12:32:01,073 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-04 12:32:01,073 ] 207 root - INFO - \u26a1 Language detection: 0.248s
[ 2025-08-04 12:32:01,073 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 12:32:01,073 ] 513 root - INFO - Enhanced web search query: current events as of 2025 as of 2025
[ 2025-08-04 12:32:01,074 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 12:32:01,074 ] 45 root - INFO - \U0001f310 Fast web search: 'current events as of 2025 as o...'
[ 2025-08-04 12:32:01,335 ] 513 root - INFO - Enhanced web search query: Prime Minister of India as of 2025 as of 2025
[ 2025-08-04 12:32:01,336 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 12:32:01,337 ] 45 root - INFO - \U0001f310 Fast web search: 'Prime Minister of India as of ...'
[ 2025-08-04 12:32:03,475 ] 59 root - INFO - \u26a1 Web search: 2.40s, 2 results
[ 2025-08-04 12:32:03,476 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4019 seconds.
[ 2025-08-04 12:32:03,476 ] 525 root - INFO - \U0001f310 Web search: 2.65s, 2 results
[ 2025-08-04 12:32:04,221 ] 59 root - INFO - \u26a1 Web search: 2.88s, 2 results
[ 2025-08-04 12:32:04,223 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.8863 seconds.
[ 2025-08-04 12:32:04,223 ] 525 root - INFO - \U0001f310 Web search: 2.89s, 2 results
[ 2025-08-04 12:32:04,834 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 12:32:04,838 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-04 12:32:07,738 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 12:32:07,740 ] 159 root - INFO - \u26a1 New response generated in 2.90s
[ 2025-08-04 12:32:07,740 ] 51 root - INFO - Vector DB search completed in 2.90s
[ 2025-08-04 12:32:07,741 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 12:32:07,742 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.9083 seconds.
[ 2025-08-04 12:32:07,743 ] 457 root - INFO - \u2705 Vector search: 2.91s
[ 2025-08-04 12:32:08,370 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 12:32:08,371 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG (Retrieval-Augmented Generation) technical doc...'
[ 2025-08-04 12:32:10,313 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 12:32:10,313 ] 159 root - INFO - \u26a1 New response generated in 1.94s
[ 2025-08-04 12:32:10,314 ] 51 root - INFO - Vector DB search completed in 1.94s
[ 2025-08-04 12:32:10,314 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 12:32:10,315 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.9449 seconds.
[ 2025-08-04 12:32:10,316 ] 457 root - INFO - \u2705 Vector search: 1.95s
[ 2025-08-04 12:32:10,596 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 12:32:10,596 ] 37 root - INFO - \U0001f50d Vector DB search for: 'NLP (Natural Language Processing) technical docume...'
[ 2025-08-04 12:32:11,691 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 12:32:11,693 ] 159 root - INFO - \u26a1 New response generated in 1.10s
[ 2025-08-04 12:32:11,693 ] 51 root - INFO - Vector DB search completed in 1.10s
[ 2025-08-04 12:32:11,695 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 12:32:11,695 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0991 seconds.
[ 2025-08-04 12:32:11,698 ] 457 root - INFO - \u2705 Vector search: 1.10s
[ 2025-08-04 12:32:11,702 ] 1644 livekit.agents - WARNING - maximum number of function calls steps reached
[ 2025-08-04 12:32:21,039 ] 513 root - INFO - Enhanced web search query: Chief Minister of Andhra Pradesh as of 2025 as of 2025
[ 2025-08-04 12:32:21,040 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 12:32:21,040 ] 45 root - INFO - \U0001f310 Fast web search: 'Chief Minister of Andhra Prade...'
[ 2025-08-04 12:32:23,500 ] 59 root - INFO - \u26a1 Web search: 2.46s, 2 results
[ 2025-08-04 12:32:23,501 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4613 seconds.
[ 2025-08-04 12:32:23,501 ] 525 root - INFO - \U0001f310 Web search: 2.46s, 2 results
[ 2025-08-04 12:32:38,227 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 12:32:38,228 ] 37 root - INFO - \U0001f50d Vector DB search for: 'AVB switchgear operating voltage...'
[ 2025-08-04 12:32:40,105 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 12:32:40,106 ] 159 root - INFO - \u26a1 New response generated in 1.88s
[ 2025-08-04 12:32:40,106 ] 51 root - INFO - Vector DB search completed in 1.88s
[ 2025-08-04 12:32:40,107 ] 52 root - INFO - Results relevant: False
[ 2025-08-04 12:32:40,107 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.8801 seconds.
[ 2025-08-04 12:32:40,108 ] 460 root - INFO - \u274c No results: 1.88s
[ 2025-08-04 12:33:02,067 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 12:33:02,067 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear operating voltage...'
[ 2025-08-04 12:33:03,473 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 12:33:03,474 ] 159 root - INFO - \u26a1 New response generated in 1.41s
[ 2025-08-04 12:33:03,475 ] 51 root - INFO - Vector DB search completed in 1.41s
[ 2025-08-04 12:33:03,475 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 12:33:03,475 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.4080 seconds.
[ 2025-08-04 12:33:03,476 ] 457 root - INFO - \u2705 Vector search: 1.41s
[ 2025-08-04 12:33:26,573 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 12:33:26,580 ] 465 livekit.agents - WARNING - rotate_segment called while previous segment is still being rotated
[ 2025-08-04 12:33:49,824 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-04 13:12:06,380 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 13:12:06,590 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:12:06,595 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:12:08,815 ] 666 root - INFO - Call logging started for call ID: SCL_m5iWpyBJje7a
[ 2025-08-04 13:12:13,489 ] 246 livekit.agents - WARNING - failed to synthesize speech, retrying in 0.1s
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\tts\tts.py", line 235, in _main_task
    raise APIError("no audio frames were pushed")
livekit.agents._exceptions.APIError: no audio frames were pushed (body=None, retryable=True)
[ 2025-08-04 13:12:16,525 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 13:12:16,529 ] 755 root - INFO - Call logging ended for call ID: SCL_m5iWpyBJje7a
[ 2025-08-04 13:12:16,529 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 13:12:48,555 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-04 13:12:48,556 ] 509 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 13:12:48,557 ] 513 root - INFO - Enhanced web search query: current Chief Minister of West Bengal as of 2025 as of 2025
[ 2025-08-04 13:12:48,558 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:12:48,559 ] 45 root - INFO - \U0001f310 Fast web search: 'current Chief Minister of West...'
[ 2025-08-04 13:12:51,503 ] 59 root - INFO - \u26a1 Web search: 2.94s, 2 results
[ 2025-08-04 13:12:51,504 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.9458 seconds.
[ 2025-08-04 13:12:51,506 ] 525 root - INFO - \U0001f310 Web search: 2.96s, 2 results
[ 2025-08-04 13:12:51,739 ] 513 root - INFO - Enhanced web search query: Mamata Banerjee current Chief Minister of West Bengal as of 2025 as of 2025
[ 2025-08-04 13:12:51,740 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:12:51,740 ] 45 root - INFO - \U0001f310 Fast web search: 'Mamata Banerjee current Chief ...'
[ 2025-08-04 13:12:54,121 ] 59 root - INFO - \u26a1 Web search: 2.38s, 2 results
[ 2025-08-04 13:12:54,122 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3819 seconds.
[ 2025-08-04 13:12:54,123 ] 525 root - INFO - \U0001f310 Web search: 2.38s, 2 results
[ 2025-08-04 13:12:54,508 ] 513 root - INFO - Enhanced web search query: Mamata Banerjee Chief Minister of West Bengal as of 2025 as of 2025
[ 2025-08-04 13:12:54,510 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:12:54,512 ] 45 root - INFO - \U0001f310 Fast web search: 'Mamata Banerjee Chief Minister...'
[ 2025-08-04 13:12:56,942 ] 59 root - INFO - \u26a1 Web search: 2.43s, 2 results
[ 2025-08-04 13:12:56,943 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4337 seconds.
[ 2025-08-04 13:12:56,944 ] 525 root - INFO - \U0001f310 Web search: 2.44s, 2 results
[ 2025-08-04 13:13:23,449 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:13:23,450 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear operating voltage...'
[ 2025-08-04 13:13:23,451 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-04 13:13:23,451 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-04 13:13:23,451 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:13:23,453 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0033 seconds.
[ 2025-08-04 13:13:23,454 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-04 13:13:35,990 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 13:13:36,018 ] 465 livekit.agents - WARNING - rotate_segment called while previous segment is still being rotated
[ 2025-08-04 13:13:59,166 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
[ 2025-08-04 13:16:55,784 ] 855 livekit.agents - INFO - received job request
[ 2025-08-04 13:16:55,908 ] 151 livekit.agents - INFO - initializing job runner
[ 2025-08-04 13:16:55,909 ] 160 livekit.agents - INFO - job runner initialized
[ 2025-08-04 13:16:58,239 ] 666 root - INFO - Call logging started for call ID: SCL_ZyBw5ssKdNBV
[ 2025-08-04 13:17:02,197 ] 746 root - ERROR - Voice agent error: 'super' object has no attribute 'wait_for_completion'
[ 2025-08-04 13:17:02,208 ] 755 root - INFO - Call logging ended for call ID: SCL_ZyBw5ssKdNBV
[ 2025-08-04 13:17:02,209 ] 262 livekit.agents - ERROR - unhandled exception while running the job task
Traceback (most recent call last):
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 743, in entrypoint
    await session.wait_for_completion()
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 258, in wait_for_completion
    await super().wait_for_completion()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'super' object has no attribute 'wait_for_completion'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\opentelemetry\util\_decorator.py", line 71, in async_wrapper
    return await func(*args, **kwargs)  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\ipc\job_proc_lazy_main.py", line 240, in _traceable_entrypoint
    await self._job_entrypoint_fnc(job_ctx)
  File "D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py", line 747, in entrypoint
    raise CustomException(e, sys)
src.exception.CustomException: Error occurred python script name [D:\integrate_real_frontend\dev-calling-agent\langgraph-agent.py] line number [743] error message ['super' object has no attribute 'wait_for_completion']
[ 2025-08-04 13:17:11,452 ] 180 root - INFO - \u26a1 Langdetect: en
[ 2025-08-04 13:17:11,453 ] 442 root - INFO - \u26a1 Fast language detection: English (en)
[ 2025-08-04 13:17:11,454 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:11,454 ] 37 root - INFO - \U0001f50d Vector DB search for: 'project record...'
[ 2025-08-04 13:17:14,327 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:14,328 ] 159 root - INFO - \u26a1 New response generated in 2.87s
[ 2025-08-04 13:17:14,329 ] 51 root - INFO - Vector DB search completed in 2.88s
[ 2025-08-04 13:17:14,330 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:14,330 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.8763 seconds.
[ 2025-08-04 13:17:14,331 ] 457 root - INFO - \u2705 Vector search: 2.90s
[ 2025-08-04 13:17:15,236 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:15,237 ] 37 root - INFO - \U0001f50d Vector DB search for: 'project record...'
[ 2025-08-04 13:17:15,237 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-04 13:17:15,238 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-04 13:17:15,239 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:15,240 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0040 seconds.
[ 2025-08-04 13:17:15,243 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-04 13:17:15,510 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:15,512 ] 37 root - INFO - \U0001f50d Vector DB search for: 'RAG model performance on open-domain question answ...'
[ 2025-08-04 13:17:16,543 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:16,544 ] 159 root - INFO - \u26a1 New response generated in 1.03s
[ 2025-08-04 13:17:16,544 ] 51 root - INFO - Vector DB search completed in 1.03s
[ 2025-08-04 13:17:16,544 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:16,544 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.0337 seconds.
[ 2025-08-04 13:17:16,545 ] 457 root - INFO - \u2705 Vector search: 1.03s
[ 2025-08-04 13:17:16,681 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:16,681 ] 37 root - INFO - \U0001f50d Vector DB search for: 'project record and retrieval...'
[ 2025-08-04 13:17:17,678 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:17,678 ] 159 root - INFO - \u26a1 New response generated in 1.00s
[ 2025-08-04 13:17:17,679 ] 51 root - INFO - Vector DB search completed in 1.00s
[ 2025-08-04 13:17:17,679 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:17,679 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.9990 seconds.
[ 2025-08-04 13:17:17,679 ] 457 root - INFO - \u2705 Vector search: 1.00s
[ 2025-08-04 13:17:19,775 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:19,777 ] 37 root - INFO - \U0001f50d Vector DB search for: 'technical documentation on transformers...'
[ 2025-08-04 13:17:21,388 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:21,389 ] 159 root - INFO - \u26a1 New response generated in 1.61s
[ 2025-08-04 13:17:21,390 ] 51 root - INFO - Vector DB search completed in 1.61s
[ 2025-08-04 13:17:21,390 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:21,391 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.6166 seconds.
[ 2025-08-04 13:17:21,391 ] 457 root - INFO - \u2705 Vector search: 1.62s
[ 2025-08-04 13:17:21,429 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:21,430 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear...'
[ 2025-08-04 13:17:21,923 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:21,924 ] 37 root - INFO - \U0001f50d Vector DB search for: 'ABB switchgear technical documentation...'
[ 2025-08-04 13:17:21,926 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-04 13:17:21,928 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-04 13:17:21,929 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:21,930 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0070 seconds.
[ 2025-08-04 13:17:21,933 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-04 13:17:22,411 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:22,411 ] 159 root - INFO - \u26a1 New response generated in 0.98s
[ 2025-08-04 13:17:22,412 ] 51 root - INFO - Vector DB search completed in 0.98s
[ 2025-08-04 13:17:22,412 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:22,413 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.9848 seconds.
[ 2025-08-04 13:17:22,414 ] 457 root - INFO - \u2705 Vector search: 0.99s
[ 2025-08-04 13:17:22,539 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:22,540 ] 37 root - INFO - \U0001f50d Vector DB search for: 'company policies on data protection...'
[ 2025-08-04 13:17:22,810 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:22,810 ] 37 root - INFO - \U0001f50d Vector DB search for: 'transformer...'
[ 2025-08-04 13:17:23,295 ] 513 root - INFO - Enhanced web search query: why do we need to do this as of 2025 as of 2025
[ 2025-08-04 13:17:23,297 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:23,298 ] 45 root - INFO - \U0001f310 Fast web search: 'why do we need to do this as o...'
[ 2025-08-04 13:17:23,435 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:23,436 ] 159 root - INFO - \u26a1 New response generated in 0.90s
[ 2025-08-04 13:17:23,436 ] 51 root - INFO - Vector DB search completed in 0.90s
[ 2025-08-04 13:17:23,436 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:23,437 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.8982 seconds.
[ 2025-08-04 13:17:23,437 ] 457 root - INFO - \u2705 Vector search: 0.90s
[ 2025-08-04 13:17:25,134 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:25,135 ] 159 root - INFO - \u26a1 New response generated in 2.32s
[ 2025-08-04 13:17:25,136 ] 51 root - INFO - Vector DB search completed in 2.33s
[ 2025-08-04 13:17:25,137 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:25,137 ] 68 root - INFO - \u2705 Finished 'search_documents' in 2.3275 seconds.
[ 2025-08-04 13:17:25,138 ] 457 root - INFO - \u2705 Vector search: 2.33s
[ 2025-08-04 13:17:25,812 ] 59 root - INFO - \u26a1 Web search: 2.51s, 2 results
[ 2025-08-04 13:17:25,813 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5160 seconds.
[ 2025-08-04 13:17:25,814 ] 525 root - INFO - \U0001f310 Web search: 2.52s, 2 results
[ 2025-08-04 13:17:26,670 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:26,671 ] 37 root - INFO - \U0001f50d Vector DB search for: 'university naming conventions...'
[ 2025-08-04 13:17:26,810 ] 513 root - INFO - Enhanced web search query: why do we need to do this as of 2025 as of 2025
[ 2025-08-04 13:17:26,812 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:26,814 ] 45 root - INFO - \U0001f310 Fast web search: 'why do we need to do this as o...'
[ 2025-08-04 13:17:27,616 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:27,617 ] 159 root - INFO - \u26a1 New response generated in 0.94s
[ 2025-08-04 13:17:27,619 ] 51 root - INFO - Vector DB search completed in 0.95s
[ 2025-08-04 13:17:27,620 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:27,624 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.9542 seconds.
[ 2025-08-04 13:17:27,625 ] 457 root - INFO - \u2705 Vector search: 0.96s
[ 2025-08-04 13:17:29,183 ] 59 root - INFO - \u26a1 Web search: 2.37s, 2 results
[ 2025-08-04 13:17:29,185 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.3731 seconds.
[ 2025-08-04 13:17:29,187 ] 525 root - INFO - \U0001f310 Web search: 2.38s, 2 results
[ 2025-08-04 13:17:30,358 ] 513 root - INFO - Enhanced web search query: why do we need to do this as of 2025 as of 2025
[ 2025-08-04 13:17:30,361 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:30,364 ] 45 root - INFO - \U0001f310 Fast web search: 'why do we need to do this as o...'
[ 2025-08-04 13:17:31,239 ] 513 root - INFO - Enhanced web search query: greetings from another place as of 2025 as of 2025
[ 2025-08-04 13:17:31,241 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:31,242 ] 45 root - INFO - \U0001f310 Fast web search: 'greetings from another place a...'
[ 2025-08-04 13:17:33,093 ] 59 root - INFO - \u26a1 Web search: 2.73s, 2 results
[ 2025-08-04 13:17:33,095 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7340 seconds.
[ 2025-08-04 13:17:33,096 ] 525 root - INFO - \U0001f310 Web search: 2.74s, 2 results
[ 2025-08-04 13:17:33,705 ] 59 root - INFO - \u26a1 Web search: 2.46s, 2 results
[ 2025-08-04 13:17:33,706 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.4654 seconds.
[ 2025-08-04 13:17:33,707 ] 525 root - INFO - \U0001f310 Web search: 2.47s, 2 results
[ 2025-08-04 13:17:35,670 ] 226 livekit.agents - ERROR - error decoding audio
Traceback (most recent call last):
  File "D:\conda\envs\voice_arpan\Lib\site-packages\livekit\agents\utils\codecs\decoder.py", line 173, in _decode_loop
    container = av.open(
                ^^^^^^^^
  File "av/container/core.pyx", line 418, in av.container.core.open
  File "av/container/core.pyx", line 283, in av.container.core.Container.__cinit__
  File "av/container/core.pyx", line 303, in av.container.core.Container.err_check
  File "av/error.pyx", line 424, in av.error.err_check
av.error.EOFError: [Errno 541478725] End of file: '<none>'
[ 2025-08-04 13:17:35,715 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:35,716 ] 37 root - INFO - \U0001f50d Vector DB search for: 'greetings from another place...'
[ 2025-08-04 13:17:37,235 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:37,236 ] 159 root - INFO - \u26a1 New response generated in 1.52s
[ 2025-08-04 13:17:37,237 ] 51 root - INFO - Vector DB search completed in 1.52s
[ 2025-08-04 13:17:37,237 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:37,238 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.5222 seconds.
[ 2025-08-04 13:17:37,240 ] 457 root - INFO - \u2705 Vector search: 1.52s
[ 2025-08-04 13:17:37,841 ] 513 root - INFO - Enhanced web search query: greetings from another place as of 2025 as of 2025
[ 2025-08-04 13:17:37,843 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:37,844 ] 45 root - INFO - \U0001f310 Fast web search: 'greetings from another place a...'
[ 2025-08-04 13:17:39,840 ] 59 root - INFO - \u26a1 Web search: 2.00s, 2 results
[ 2025-08-04 13:17:39,840 ] 68 root - INFO - \u2705 Finished 'search_web' in 1.9968 seconds.
[ 2025-08-04 13:17:39,841 ] 525 root - INFO - \U0001f310 Web search: 2.00s, 2 results
[ 2025-08-04 13:17:42,170 ] 513 root - INFO - Enhanced web search query: good as of 2025 as of 2025
[ 2025-08-04 13:17:42,174 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:42,175 ] 45 root - INFO - \U0001f310 Fast web search: 'good as of 2025 as of 2025...'
[ 2025-08-04 13:17:43,762 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:43,764 ] 37 root - INFO - \U0001f50d Vector DB search for: 'university naming conventions...'
[ 2025-08-04 13:17:43,765 ] 143 root - INFO - \U0001f680 Cache hit! Response in 0.000s
[ 2025-08-04 13:17:43,767 ] 51 root - INFO - Vector DB search completed in 0.00s
[ 2025-08-04 13:17:43,768 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:43,769 ] 68 root - INFO - \u2705 Finished 'search_documents' in 0.0074 seconds.
[ 2025-08-04 13:17:43,770 ] 457 root - INFO - \u2705 Vector search: 0.01s
[ 2025-08-04 13:17:44,672 ] 513 root - INFO - Enhanced web search query: why do we use before university as of 2025 as of 2025
[ 2025-08-04 13:17:44,672 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:44,673 ] 45 root - INFO - \U0001f310 Fast web search: 'why do we use before universit...'
[ 2025-08-04 13:17:44,919 ] 59 root - INFO - \u26a1 Web search: 2.74s, 2 results
[ 2025-08-04 13:17:44,920 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7475 seconds.
[ 2025-08-04 13:17:44,921 ] 525 root - INFO - \U0001f310 Web search: 2.75s, 2 results
[ 2025-08-04 13:17:46,285 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:46,286 ] 37 root - INFO - \U0001f50d Vector DB search for: 'good...'
[ 2025-08-04 13:17:47,398 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:47,399 ] 159 root - INFO - \u26a1 New response generated in 1.11s
[ 2025-08-04 13:17:47,400 ] 51 root - INFO - Vector DB search completed in 1.11s
[ 2025-08-04 13:17:47,401 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:47,401 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.1154 seconds.
[ 2025-08-04 13:17:47,402 ] 457 root - INFO - \u2705 Vector search: 1.12s
[ 2025-08-04 13:17:47,465 ] 59 root - INFO - \u26a1 Web search: 2.79s, 2 results
[ 2025-08-04 13:17:47,467 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.7950 seconds.
[ 2025-08-04 13:17:47,468 ] 525 root - INFO - \U0001f310 Web search: 2.80s, 2 results
[ 2025-08-04 13:17:48,827 ] 513 root - INFO - Enhanced web search query: good as of 2025 as of 2025
[ 2025-08-04 13:17:48,830 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:48,830 ] 45 root - INFO - \U0001f310 Fast web search: 'good as of 2025 as of 2025...'
[ 2025-08-04 13:17:51,345 ] 59 root - INFO - \u26a1 Web search: 2.51s, 2 results
[ 2025-08-04 13:17:51,346 ] 68 root - INFO - \u2705 Finished 'search_web' in 2.5153 seconds.
[ 2025-08-04 13:17:51,347 ] 525 root - INFO - \U0001f310 Web search: 2.52s, 2 results
[ 2025-08-04 13:17:52,912 ] 62 root - INFO - \U0001f680 Starting 'search_documents'...
[ 2025-08-04 13:17:52,912 ] 37 root - INFO - \U0001f50d Vector DB search for: 'university...'
[ 2025-08-04 13:17:54,150 ] 126 root - INFO - \u26a1 Cached RAG response generated
[ 2025-08-04 13:17:54,151 ] 159 root - INFO - \u26a1 New response generated in 1.24s
[ 2025-08-04 13:17:54,151 ] 51 root - INFO - Vector DB search completed in 1.24s
[ 2025-08-04 13:17:54,151 ] 52 root - INFO - Results relevant: True
[ 2025-08-04 13:17:54,152 ] 68 root - INFO - \u2705 Finished 'search_documents' in 1.2399 seconds.
[ 2025-08-04 13:17:54,152 ] 457 root - INFO - \u2705 Vector search: 1.24s
[ 2025-08-04 13:17:54,829 ] 513 root - INFO - Enhanced web search query: no as of 2025 as of 2025
[ 2025-08-04 13:17:54,831 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:54,832 ] 45 root - INFO - \U0001f310 Fast web search: 'no as of 2025 as of 2025...'
[ 2025-08-04 13:17:55,163 ] 513 root - INFO - Enhanced web search query: thank you as of 2025 as of 2025
[ 2025-08-04 13:17:55,165 ] 62 root - INFO - \U0001f680 Starting 'search_web'...
[ 2025-08-04 13:17:55,169 ] 45 root - INFO - \U0001f310 Fast web search: 'thank you as of 2025 as of 202...'
[ 2025-08-04 13:17:59,307 ] 59 root - INFO - \u26a1 Web search: 4.14s, 2 results
[ 2025-08-04 13:17:59,307 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.1420 seconds.
[ 2025-08-04 13:17:59,308 ] 525 root - INFO - \U0001f310 Web search: 4.14s, 2 results
[ 2025-08-04 13:17:59,386 ] 59 root - INFO - \u26a1 Web search: 4.55s, 2 results
[ 2025-08-04 13:17:59,387 ] 68 root - INFO - \u2705 Finished 'search_web' in 4.5554 seconds.
[ 2025-08-04 13:17:59,388 ] 525 root - INFO - \U0001f310 Web search: 4.56s, 2 results
[ 2025-08-04 13:18:01,434 ] 425 livekit.agents - INFO - closing agent session due to participant disconnect (disable via `RoomInputOptions.close_on_disconnect=False`)
[ 2025-08-04 13:18:24,962 ] 167 livekit - WARNING - livekit::rtc_engine:453:livekit::rtc_engine - received session close: "signal client closed: \"stream closed\"" UnknownReason Resume
